<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Data Upload Interface</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    .steps {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }
    .step {
      padding: 10px 20px;
      background: #ccc;
      color: #fff;
      border-radius: 5px;
    }
    .step.active {
      background: #d20000;
    }
    .container {
      display: flex;
      gap: 20px;
    }
    .options {
      flex: 1;
    }
    .options button {
      display: block;
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      border: 1px solid #ccc;
      background: #f9f9f9;
      cursor: pointer;
    }
    .textarea-container {
      flex: 2;
    }
    textarea {
      width: 100%;
      height: 300px;
      border: 1px solid #ccc;
      padding: 10px;
      resize: none;
    }
    .proceed-button {
      margin-top: 20px;
      padding: 10px 20px;
      background: #d20000;
      color: #fff;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <div class="steps">
    <div class="step active">1. Upload Data</div>
    <div class="step">2. Check & Describe</div>
    <div class="step">3. Visualize</div>
    <div class="step">4. Publish & Embed</div>
  </div>

  <div class="container">
    <div class="options">
      <button>Copy & paste data table</button>
      <button>XLS/CSV upload</button>
      <button>Connect Google Sheet</button>
      <button>Link external data</button>
    </div>
    <div class="textarea-container">
      <textarea placeholder="Paste your data here..."></textarea>
      <button class="proceed-button">Proceed</button>
    </div>
  </div>
</body>
</html>
