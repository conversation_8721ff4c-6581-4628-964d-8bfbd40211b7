{"survey_id": "415451", "groups": [{"gid": 16, "group_name": "Úvodní otázka", "questions": [{"qid": 366, "title": "Q1", "question": "Jak dobře znáte Chotkovy sady? ", "type": "L", "answers": {}, "properties": {"qid": 366, "parent_qid": 0, "sid": 415451, "gid": 16, "type": "L", "title": "Q1", "preg": null, "other": "N", "mandatory": "Y", "encrypted": "N", "question_order": 1, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": "listradio", "modulename": "", "same_script": 0, "question": "Jak dobře znáte Chotkovy sady? ", "help": "", "script": "", "questionl10ns": {"id": 366, "qid": 366, "question": "Jak dobře znáte Chotkovy sady? ", "help": "", "script": "", "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"answer_order": "normal", "array_filter": "", "array_filter_exclude": "", "array_filter_style": "0", "cssclass": "", "display_columns": "4", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "other_comment_mandatory": "0", "other_numbers_only": "0", "other_position": "default", "other_position_code": "", "page_break": "0", "public_statistics": "0", "random_group": "", "scale_export": "0", "statistics_graphtype": "0", "statistics_showgraph": "1", "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "other_replace_text": "", "printable_help": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": {"AO01": {"answer": "<PERSON><PERSON><PERSON> ve<PERSON>i do<PERSON>ř<PERSON>", "assessment_value": 0, "scale_id": 0, "order": 0}, "AO02": {"answer": "<PERSON><PERSON><PERSON> z<PERSON> poměrně dobře", "assessment_value": 0, "scale_id": 0, "order": 1}, "AO03": {"answer": "<PERSON><PERSON><PERSON>, ale ne pří<PERSON>š dobře", "assessment_value": 0, "scale_id": 0, "order": 2}, "AO04": {"answer": "<PERSON><PERSON><PERSON> vů<PERSON> neznám / dosud jsem o něm ani ne<PERSON>/a", "assessment_value": 0, "scale_id": 0, "order": 3}}, "defaultvalue": null}}, {"qid": 367, "title": "Q2", "question": "Jak často navštěvujete Chotkovy sady?", "type": "L", "answers": {}, "properties": {"qid": 367, "parent_qid": 0, "sid": 415451, "gid": 16, "type": "L", "title": "Q2", "preg": null, "other": "N", "mandatory": "Y", "encrypted": "N", "question_order": 2, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": "listradio", "modulename": "", "same_script": 0, "question": "Jak často navštěvujete Chotkovy sady?", "help": "", "script": "", "questionl10ns": {"id": 367, "qid": 367, "question": "Jak často navštěvujete Chotkovy sady?", "help": "", "script": "", "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"answer_order": "normal", "array_filter": "", "array_filter_exclude": "", "array_filter_style": "0", "cssclass": "", "display_columns": "3", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "other_comment_mandatory": "0", "other_numbers_only": "0", "other_position": "default", "other_position_code": "", "page_break": "0", "public_statistics": "0", "random_group": "", "scale_export": "0", "statistics_graphtype": "0", "statistics_showgraph": "1", "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "other_replace_text": "", "printable_help": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": {"AO01": {"answer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assessment_value": 0, "scale_id": 0, "order": 0}, "AO02": {"answer": "Několikrát do týdne", "assessment_value": 0, "scale_id": 0, "order": 1}, "AO03": {"answer": "Několikrát do měsíce", "assessment_value": 0, "scale_id": 0, "order": 2}, "AO04": {"answer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ě (méně než jednou za měsíc)", "assessment_value": 0, "scale_id": 0, "order": 3}, "AO05": {"answer": "Byl/a jsem tam pouze jednou", "assessment_value": 0, "scale_id": 0, "order": 4}, "AO06": {"answer": "<PERSON><PERSON> jsem tam nebyl/a", "assessment_value": 0, "scale_id": 0, "order": 5}}, "defaultvalue": null}}]}, {"gid": 14, "group_name": "<PERSON><PERSON><PERSON>", "questions": [{"qid": 381, "title": "SQ001", "question": "Historický a kulturní charakter parku ", "type": "T", "answers": {}, "properties": {"qid": 381, "parent_qid": 379, "sid": 415451, "gid": 14, "type": "T", "title": "SQ001", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 0, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Historický a kulturní charakter parku ", "help": "", "script": null, "questionl10ns": {"id": 381, "qid": 381, "question": "Historický a kulturní charakter parku ", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 369, "title": "SQ001", "question": "Jako p<PERSON>ů<PERSON>d", "type": "T", "answers": {}, "properties": {"qid": 369, "parent_qid": 368, "sid": 415451, "gid": 14, "type": "T", "title": "SQ001", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 0, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Jako p<PERSON>ů<PERSON>d", "help": null, "script": null, "questionl10ns": {"id": 369, "qid": 369, "question": "Jako p<PERSON>ů<PERSON>d", "help": null, "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 390, "title": "SQ001", "question": "Neosvětlená nebo nepřehledná místa ", "type": "T", "answers": {}, "properties": {"qid": 390, "parent_qid": 389, "sid": 415451, "gid": 14, "type": "T", "title": "SQ001", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 0, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Neosvětlená nebo nepřehledná místa ", "help": "", "script": null, "questionl10ns": {"id": 390, "qid": 390, "question": "Neosvětlená nebo nepřehledná místa ", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 382, "title": "SQ002", "question": "Zeleň a vzrostlé stromy", "type": "T", "answers": {}, "properties": {"qid": 382, "parent_qid": 379, "sid": 415451, "gid": 14, "type": "T", "title": "SQ002", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 1, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Zeleň a vzrostlé stromy", "help": "", "script": null, "questionl10ns": {"id": 382, "qid": 382, "question": "Zeleň a vzrostlé stromy", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 370, "title": "SQ002", "question": "Pro procházky", "type": "T", "answers": {}, "properties": {"qid": 370, "parent_qid": 368, "sid": 415451, "gid": 14, "type": "T", "title": "SQ002", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 1, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Pro procházky", "help": null, "script": null, "questionl10ns": {"id": 370, "qid": 370, "question": "Pro procházky", "help": null, "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 368, "title": "Q3", "question": "K čemu Chotkovy sady využíváte? ", "type": "F", "answers": {}, "properties": {"qid": 368, "parent_qid": 0, "sid": 415451, "gid": 14, "type": "F", "title": "Q3", "preg": null, "other": "N", "mandatory": "Y", "encrypted": "N", "question_order": 1, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": "arrays/array", "modulename": "", "same_script": 0, "question": "K čemu Chotkovy sady využíváte? ", "help": "<script type=\"text/javascript\" charset=\"utf-8\">\r\n$(document).on('ready pjax:scriptcomplete', function(){\r\n    /**\r\n     * Zamíchá subotázky v array otázce kromě posledních X položek\r\n     * @param {number} fixedCount - Počet posledn<PERSON><PERSON>, kter<PERSON> z<PERSON> nezamíchané\r\n     */\r\n    function randomizeArrayQuestionRows(fixedCount = 0) {\r\n        // Najít všechny array otázky\r\n        $('.array-flexible-row').each(function() {\r\n            console.log('Zpracovávám otázku:', $(this).attr('id'));\r\n            \r\n            // Získat tbody s řádky\r\n            const $tbody = $(this).find('tbody');\r\n            if ($tbody.length === 0) {\r\n                console.log('Nenalezen tbody element');\r\n                return;\r\n            }\r\n\r\n            // Převést řádky na pole\r\n            let $rows = $tbody.find('tr').toArray();\r\n            console.log('<PERSON><PERSON><PERSON>:', $rows.length);\r\n            \r\n            if (fixedCount >= $rows.length) {\r\n                console.log('Počet fixních řádků je větší nebo roven celkovému počtu řádků');\r\n                return;\r\n            }\r\n\r\n            // Rozdělit na části k zamíchání a fixní\r\n            const toRandomize = $rows.slice(0, $rows.length - fixedCount);\r\n            const fixed = $rows.slice($rows.length - fixedCount);\r\n            \r\n            console.log('Řádků k zamíchání:', toRandomize.length);\r\n            console.log('Fixních řádků:', fixed.length);\r\n\r\n            // Zamíchat první část\r\n            for (let i = toRandomize.length - 1; i > 0; i--) {\r\n                const j = Math.floor(Math.random() * (i + 1));\r\n                [toRandomize[i], toRandomize[j]] = [toRandomize[j], toRandomize[i]];\r\n            }\r\n\r\n            // Vyčistit tbody a vložit zamíchané a fixní řádky zpět\r\n            $tbody.empty().append(toRandomize.concat(fixed));\r\n            \r\n            console.log('Zamíchání dokončeno');\r\n        });\r\n    }\r\n\r\n    // Spustit\r\n    randomizeArrayQuestionRows(1); // Příklad: poslední 2 položky zůstanou fixní\r\n});\r\n</script>", "script": "", "questionl10ns": {"id": 368, "qid": 368, "question": "K čemu Chotkovy sady využíváte? ", "help": "<script type=\"text/javascript\" charset=\"utf-8\">\r\n$(document).on('ready pjax:scriptcomplete', function(){\r\n    /**\r\n     * Zamíchá subotázky v array otázce kromě posledních X položek\r\n     * @param {number} fixedCount - Počet posledn<PERSON><PERSON>, kter<PERSON> z<PERSON> nezamíchané\r\n     */\r\n    function randomizeArrayQuestionRows(fixedCount = 0) {\r\n        // Najít všechny array otázky\r\n        $('.array-flexible-row').each(function() {\r\n            console.log('Zpracovávám otázku:', $(this).attr('id'));\r\n            \r\n            // Získat tbody s řádky\r\n            const $tbody = $(this).find('tbody');\r\n            if ($tbody.length === 0) {\r\n                console.log('Nenalezen tbody element');\r\n                return;\r\n            }\r\n\r\n            // Převést řádky na pole\r\n            let $rows = $tbody.find('tr').toArray();\r\n            console.log('<PERSON><PERSON><PERSON>:', $rows.length);\r\n            \r\n            if (fixedCount >= $rows.length) {\r\n                console.log('Počet fixních řádků je větší nebo roven celkovému počtu řádků');\r\n                return;\r\n            }\r\n\r\n            // Rozdělit na části k zamíchání a fixní\r\n            const toRandomize = $rows.slice(0, $rows.length - fixedCount);\r\n            const fixed = $rows.slice($rows.length - fixedCount);\r\n            \r\n            console.log('Řádků k zamíchání:', toRandomize.length);\r\n            console.log('Fixních řádků:', fixed.length);\r\n\r\n            // Zamíchat první část\r\n            for (let i = toRandomize.length - 1; i > 0; i--) {\r\n                const j = Math.floor(Math.random() * (i + 1));\r\n                [toRandomize[i], toRandomize[j]] = [toRandomize[j], toRandomize[i]];\r\n            }\r\n\r\n            // Vyčistit tbody a vložit zamíchané a fixní řádky zpět\r\n            $tbody.empty().append(toRandomize.concat(fixed));\r\n            \r\n            console.log('Zamíchání dokončeno');\r\n        });\r\n    }\r\n\r\n    // Spustit\r\n    randomizeArrayQuestionRows(1); // Příklad: poslední 2 položky zůstanou fixní\r\n});\r\n</script>", "script": "", "language": "cs"}, "available_answers": {"SQ009": "K něčemu jinému, vypište", "SQ007": "Pro trávení času s dětmi", "SQ008": "Pro sportovní aktivity ve skupině", "SQ005": "Pro venčení či cvičení psa ", "SQ006": "<PERSON><PERSON><PERSON><PERSON>", "SQ003": "Pro odpočinek", "SQ004": "Pro aktivní pohyb", "SQ001": "Jako p<PERSON>ů<PERSON>d", "SQ002": "Pro procházky"}, "subquestions": {"377": {"title": "SQ009", "question": "K něčemu jinému, vypište", "scale_id": 0}, "375": {"title": "SQ007", "question": "Pro trávení času s dětmi", "scale_id": 0}, "376": {"title": "SQ008", "question": "Pro sportovní aktivity ve skupině", "scale_id": 0}, "373": {"title": "SQ005", "question": "Pro venčení či cvičení psa ", "scale_id": 0}, "374": {"title": "SQ006", "question": "<PERSON><PERSON><PERSON><PERSON>", "scale_id": 0}, "371": {"title": "SQ003", "question": "Pro odpočinek", "scale_id": 0}, "372": {"title": "SQ004", "question": "Pro aktivní pohyb", "scale_id": 0}, "369": {"title": "SQ001", "question": "Jako p<PERSON>ů<PERSON>d", "scale_id": 0}, "370": {"title": "SQ002", "question": "Pro procházky", "scale_id": 0}}, "attributes": {"answer_width": "", "array_filter": "", "array_filter_exclude": "", "array_filter_style": "0", "cssclass": "", "em_validation_q": "", "exclude_all_others": "", "hidden": "0", "hide_tip": "0", "max_answers": "", "min_answers": "", "page_break": "0", "public_statistics": "0", "random_group": "", "random_order": "0", "repeat_headings": "", "scale_export": "0", "statistics_graphtype": "0", "statistics_showgraph": "1", "use_dropdown": "0"}, "attributes_lang": {"em_validation_q_tip": "", "printable_help": ""}, "answeroptions": {"AO01": {"answer": "Ano, využívám", "assessment_value": 0, "scale_id": 0, "order": 0}, "AO02": {"answer": "Ne, nevyužívám", "assessment_value": 0, "scale_id": 0, "order": 1}, "AO03": {"answer": "<PERSON><PERSON><PERSON><PERSON>, nechci odpovědět", "assessment_value": 0, "scale_id": 0, "order": 2}}, "defaultvalue": null}}, {"qid": 391, "title": "SQ002", "question": "<PERSON><PERSON><PERSON>, kde hrozí kolize pěších s cyklisty nebo dalšími druhy dopravy  ", "type": "T", "answers": {}, "properties": {"qid": 391, "parent_qid": 389, "sid": 415451, "gid": 14, "type": "T", "title": "SQ002", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 1, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "<PERSON><PERSON><PERSON>, kde hrozí kolize pěších s cyklisty nebo dalšími druhy dopravy  ", "help": "", "script": null, "questionl10ns": {"id": 391, "qid": 391, "question": "<PERSON><PERSON><PERSON>, kde hrozí kolize pěších s cyklisty nebo dalšími druhy dopravy  ", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 383, "title": "SQ003", "question": "Výhledy na město", "type": "T", "answers": {}, "properties": {"qid": 383, "parent_qid": 379, "sid": 415451, "gid": 14, "type": "T", "title": "SQ003", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 2, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Výhledy na město", "help": "", "script": null, "questionl10ns": {"id": 383, "qid": 383, "question": "Výhledy na město", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 378, "title": "Q3a", "question": "Jiné aktivity", "type": "S", "answers": {}, "properties": {"qid": 378, "parent_qid": 0, "sid": 415451, "gid": 14, "type": "S", "title": "Q3a", "preg": "", "other": "N", "mandatory": "Y", "encrypted": "N", "question_order": 2, "scale_id": 0, "same_default": 0, "relevance": "((Q3_SQ009.NAOK == \"AO01\"))", "question_theme_name": "shortfreetext", "modulename": "", "same_script": 0, "question": "Jiné aktivity", "help": "", "script": "", "questionl10ns": {"id": 378, "qid": 378, "question": "Jiné aktivity", "help": "", "script": "", "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": "", "location_city": "0", "location_country": "0", "location_defaultcoordinates": "", "location_mapheight": "300", "location_mapservice": "0", "location_mapwidth": "500", "location_mapzoom": "11", "location_nodefaultfromip": "0", "location_postal": "0", "location_state": "0", "maximum_chars": "", "numbers_only": "0", "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "statistics_showmap": "1", "text_input_width": "", "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "prefix": "", "suffix": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 371, "title": "SQ003", "question": "Pro odpočinek", "type": "T", "answers": {}, "properties": {"qid": 371, "parent_qid": 368, "sid": 415451, "gid": 14, "type": "T", "title": "SQ003", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 2, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Pro odpočinek", "help": null, "script": null, "questionl10ns": {"id": 371, "qid": 371, "question": "Pro odpočinek", "help": null, "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 392, "title": "SQ003", "question": "Zničený či chybějící mobiliář", "type": "T", "answers": {}, "properties": {"qid": 392, "parent_qid": 389, "sid": 415451, "gid": 14, "type": "T", "title": "SQ003", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 2, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Zničený či chybějící mobiliář", "help": "", "script": null, "questionl10ns": {"id": 392, "qid": 392, "question": "Zničený či chybějící mobiliář", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 384, "title": "SQ004", "question": "<PERSON><PERSON><PERSON><PERSON>", "type": "T", "answers": {}, "properties": {"qid": 384, "parent_qid": 379, "sid": 415451, "gid": 14, "type": "T", "title": "SQ004", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 3, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "<PERSON><PERSON><PERSON><PERSON>", "help": "", "script": null, "questionl10ns": {"id": 384, "qid": 384, "question": "<PERSON><PERSON><PERSON><PERSON>", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 379, "title": "Q4", "question": "<PERSON><PERSON><PERSON> j<PERSON>u podle Vás nejvýznamnějš<PERSON> hodnoty parku? {((count(self.NAOK) == 3) or (Q4_SQ008 == \"Y\"))}", "type": "M", "answers": {}, "properties": {"qid": 379, "parent_qid": 0, "sid": 415451, "gid": 14, "type": "M", "title": "Q4", "preg": null, "other": "Y", "mandatory": "Y", "encrypted": "N", "question_order": 3, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": "multiplechoice", "modulename": "", "same_script": 0, "question": "<PERSON><PERSON><PERSON> j<PERSON>u podle Vás nejvýznamnějš<PERSON> hodnoty parku? {((count(self.NAOK) == 3) or (Q4_SQ008 == \"Y\"))}", "help": "<script type=\"text/javascript\" charset=\"utf-8\">\r\n    $(document).on('ready pjax:scriptcomplete', function () {\r\n        console.log('Script initialized');\r\n\r\n        var qID = '{QID}'; // Dynamické ID otázky\r\n        var fixedAnswers = 1; // Počet odpovědí fixovaných na konec (kromě \"Jiné\")\r\n        var otherFixed = true; // Pokud má být \"Jiné\" na poslední pozici\r\n\r\n        // Najděte otázku podle ID\r\n        var $question = $('#question' + qID);\r\n        if (!$question.length) {\r\n            console.error('Otázka s ID ' + qID + ' nebyla nalezena.');\r\n            return;\r\n        }\r\n        console.log('Otázka nalezena:', $question);\r\n\r\n        // Najděte všechny odpovědi (li.answer-item)\r\n        var $answerItems = $question.find('li.answer-item');\r\n        if (!$answerItems.length) {\r\n            console.error('<PERSON><PERSON><PERSON><PERSON> odpovědi nebyly nale<PERSON>.');\r\n            return;\r\n        }\r\n        console.log('<PERSON><PERSON><PERSON><PERSON> odpo<PERSON>ědi:', $answerItems);\r\n\r\n        var $otherInput = $question.find('input[type=\"text\"]'); // \"Jiné\" input\r\n        var hasOther = $otherInput.length > 0;\r\n        var $answersList = $question.find('ul'); // Seznam odpovědí\r\n\r\n        if (!$answersList.length) {\r\n            console.error('Seznam odpovědí (ul) nebyl nalezen.');\r\n            return;\r\n        }\r\n\r\n        // Oddělte položky \"Jiné\" a fixní poslední odpověď (např. SQ008)\r\n        var $otherAnswerItem = hasOther ? $otherInput.closest('li.answer-item') : null;\r\n        var $fixedAnswerItem = $answerItems.filter('[id*=\"SQ008\"]');\r\n\r\n        // Zamíchejte odpovědi kromě těch, které jsou fixní (včetně \"Jiné\")\r\n        var $remainingItems = $answerItems.not($fixedAnswerItem).not($otherAnswerItem);\r\n        console.log('Zbývající odpovědi pro rotaci:', $remainingItems);\r\n\r\n        // Funkce pro zamíchání pole odpovědí\r\n        function shuffleArray(array) {\r\n            for (var i = array.length - 1; i > 0; i--) {\r\n                var j = Math.floor(Math.random() * (i + 1));\r\n                [array[i], array[j]] = [array[j], array[i]]; // swap\r\n            }\r\n        }\r\n\r\n        // Zamíchání odpovědí (kromě fixních)\r\n        var shuffledItems = $remainingItems.toArray();\r\n        shuffleArray(shuffledItems);\r\n\r\n        // Přesuneme zamíchané položky zpět do seznamu\r\n        $.each(shuffledItems, function(index, item) {\r\n            $(item).appendTo($answersList); // Znovu přidejte, aby se zachovalo pořadí\r\n        });\r\n\r\n        // Přesuňte fixní odpověď (např. SQ008) na předposlední místo\r\n        if ($fixedAnswerItem.length) {\r\n            console.log('Fixní odpověď k přesunutí:', $fixedAnswerItem);\r\n            $fixedAnswerItem.appendTo($answersList);\r\n        }\r\n\r\n        // Přesuňte \"Jiné\" na konec\r\n        if (otherFixed && $otherAnswerItem && $otherAnswerItem.length) {\r\n            console.log('\"Jiné\" odpověď k přesunování:', $otherAnswerItem);\r\n            $otherAnswerItem.appendTo($answersList);\r\n        }\r\n\r\n        console.log('Script execution completed.');\r\n    });\r\n</script>", "script": "", "questionl10ns": {"id": 379, "qid": 379, "question": "<PERSON><PERSON><PERSON> j<PERSON>u podle Vás nejvýznamnějš<PERSON> hodnoty parku? {((count(self.NAOK) == 3) or (Q4_SQ008 == \"Y\"))}", "help": "<script type=\"text/javascript\" charset=\"utf-8\">\r\n    $(document).on('ready pjax:scriptcomplete', function () {\r\n        console.log('Script initialized');\r\n\r\n        var qID = '{QID}'; // Dynamické ID otázky\r\n        var fixedAnswers = 1; // Počet odpovědí fixovaných na konec (kromě \"Jiné\")\r\n        var otherFixed = true; // Pokud má být \"Jiné\" na poslední pozici\r\n\r\n        // Najděte otázku podle ID\r\n        var $question = $('#question' + qID);\r\n        if (!$question.length) {\r\n            console.error('Otázka s ID ' + qID + ' nebyla nalezena.');\r\n            return;\r\n        }\r\n        console.log('Otázka nalezena:', $question);\r\n\r\n        // Najděte všechny odpovědi (li.answer-item)\r\n        var $answerItems = $question.find('li.answer-item');\r\n        if (!$answerItems.length) {\r\n            console.error('<PERSON><PERSON><PERSON><PERSON> odpovědi nebyly nale<PERSON>.');\r\n            return;\r\n        }\r\n        console.log('<PERSON><PERSON><PERSON><PERSON> odpo<PERSON>ědi:', $answerItems);\r\n\r\n        var $otherInput = $question.find('input[type=\"text\"]'); // \"Jiné\" input\r\n        var hasOther = $otherInput.length > 0;\r\n        var $answersList = $question.find('ul'); // Seznam odpovědí\r\n\r\n        if (!$answersList.length) {\r\n            console.error('Seznam odpovědí (ul) nebyl nalezen.');\r\n            return;\r\n        }\r\n\r\n        // Oddělte položky \"Jiné\" a fixní poslední odpověď (např. SQ008)\r\n        var $otherAnswerItem = hasOther ? $otherInput.closest('li.answer-item') : null;\r\n        var $fixedAnswerItem = $answerItems.filter('[id*=\"SQ008\"]');\r\n\r\n        // Zamíchejte odpovědi kromě těch, které jsou fixní (včetně \"Jiné\")\r\n        var $remainingItems = $answerItems.not($fixedAnswerItem).not($otherAnswerItem);\r\n        console.log('Zbývající odpovědi pro rotaci:', $remainingItems);\r\n\r\n        // Funkce pro zamíchání pole odpovědí\r\n        function shuffleArray(array) {\r\n            for (var i = array.length - 1; i > 0; i--) {\r\n                var j = Math.floor(Math.random() * (i + 1));\r\n                [array[i], array[j]] = [array[j], array[i]]; // swap\r\n            }\r\n        }\r\n\r\n        // Zamíchání odpovědí (kromě fixních)\r\n        var shuffledItems = $remainingItems.toArray();\r\n        shuffleArray(shuffledItems);\r\n\r\n        // Přesuneme zamíchané položky zpět do seznamu\r\n        $.each(shuffledItems, function(index, item) {\r\n            $(item).appendTo($answersList); // Znovu přidejte, aby se zachovalo pořadí\r\n        });\r\n\r\n        // Přesuňte fixní odpověď (např. SQ008) na předposlední místo\r\n        if ($fixedAnswerItem.length) {\r\n            console.log('Fixní odpověď k přesunutí:', $fixedAnswerItem);\r\n            $fixedAnswerItem.appendTo($answersList);\r\n        }\r\n\r\n        // Přesuňte \"Jiné\" na konec\r\n        if (otherFixed && $otherAnswerItem && $otherAnswerItem.length) {\r\n            console.log('\"Jiné\" odpověď k přesunování:', $otherAnswerItem);\r\n            $otherAnswerItem.appendTo($answersList);\r\n        }\r\n\r\n        console.log('Script execution completed.');\r\n    });\r\n</script>", "script": "", "language": "cs"}, "available_answers": {"SQ006": "Zázemí pro sport a pohybové aktivity", "SQ004": "<PERSON><PERSON><PERSON><PERSON>", "SQ005": "Prostorové uspořádání a kompozice parku (cestní s<PERSON>, komponované p<PERSON>ůhl<PERSON>y, umístěn<PERSON> s<PERSON>.)", "SQ002": "Zeleň a vzrostlé stromy", "SQ003": "Výhledy na město", "SQ001": "Historický a kulturní charakter parku ", "SQ007": "Návaznost na okolí", "SQ008": "<PERSON><PERSON><PERSON><PERSON>, nechci odpovědět"}, "subquestions": {"386": {"title": "SQ006", "question": "Zázemí pro sport a pohybové aktivity", "scale_id": 0}, "384": {"title": "SQ004", "question": "<PERSON><PERSON><PERSON><PERSON>", "scale_id": 0}, "385": {"title": "SQ005", "question": "Prostorové uspořádání a kompozice parku (cestní s<PERSON>, komponované p<PERSON>ůhl<PERSON>y, umístěn<PERSON> s<PERSON>.)", "scale_id": 0}, "382": {"title": "SQ002", "question": "Zeleň a vzrostlé stromy", "scale_id": 0}, "383": {"title": "SQ003", "question": "Výhledy na město", "scale_id": 0}, "381": {"title": "SQ001", "question": "Historický a kulturní charakter parku ", "scale_id": 0}, "387": {"title": "SQ007", "question": "Návaznost na okolí", "scale_id": 0}, "388": {"title": "SQ008", "question": "<PERSON><PERSON><PERSON><PERSON>, nechci odpovědět", "scale_id": 0}}, "attributes": {"array_filter": "", "array_filter_exclude": "", "array_filter_style": "0", "assessment_value": "1", "cssclass": "", "display_columns": "", "em_validation_q": "((count(self.NAOK) == 3) or (Q4_SQ008 == \"Y\"))", "exclude_all_others": "SQ008", "exclude_all_others_auto": "0", "hidden": "0", "hide_tip": "0", "max_answers": "", "min_answers": "", "other_numbers_only": "0", "other_position": "end", "other_position_code": "", "page_break": "0", "public_statistics": "0", "random_group": "", "random_order": "0", "scale_export": "0", "statistics_graphtype": "0", "statistics_showgraph": "1"}, "attributes_lang": {"em_validation_q_tip": "{if(Q4_SQ008 != \"Y\", \"Ze seznamu níže vyberte prosím 3 hodnoty, které považujete za nejvýznamnější.\", \"\")}", "other_replace_text": "<PERSON>é, vypište:", "printable_help": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 372, "title": "SQ004", "question": "Pro aktivní pohyb", "type": "T", "answers": {}, "properties": {"qid": 372, "parent_qid": 368, "sid": 415451, "gid": 14, "type": "T", "title": "SQ004", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 3, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Pro aktivní pohyb", "help": null, "script": null, "questionl10ns": {"id": 372, "qid": 372, "question": "Pro aktivní pohyb", "help": null, "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 393, "title": "SQ004", "question": "Zanedbaná zeleň", "type": "T", "answers": {}, "properties": {"qid": 393, "parent_qid": 389, "sid": 415451, "gid": 14, "type": "T", "title": "SQ004", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 3, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Zanedbaná zeleň", "help": "", "script": null, "questionl10ns": {"id": 393, "qid": 393, "question": "Zanedbaná zeleň", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 385, "title": "SQ005", "question": "Prostorové uspořádání a kompozice parku (cestní s<PERSON>, komponované p<PERSON>ůhl<PERSON>y, umístěn<PERSON> s<PERSON>.)", "type": "T", "answers": {}, "properties": {"qid": 385, "parent_qid": 379, "sid": 415451, "gid": 14, "type": "T", "title": "SQ005", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 4, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Prostorové uspořádání a kompozice parku (cestní s<PERSON>, komponované p<PERSON>ůhl<PERSON>y, umístěn<PERSON> s<PERSON>.)", "help": "", "script": null, "questionl10ns": {"id": 385, "qid": 385, "question": "Prostorové uspořádání a kompozice parku (cestní s<PERSON>, komponované p<PERSON>ůhl<PERSON>y, umístěn<PERSON> s<PERSON>.)", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 389, "title": "Q5", "question": "<PERSON><PERSON><PERSON> j<PERSON>u podle Vás největší problémy území?", "type": "M", "answers": {}, "properties": {"qid": 389, "parent_qid": 0, "sid": 415451, "gid": 14, "type": "M", "title": "Q5", "preg": null, "other": "Y", "mandatory": "Y", "encrypted": "N", "question_order": 4, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": "multiplechoice", "modulename": "", "same_script": 0, "question": "<PERSON><PERSON><PERSON> j<PERSON>u podle Vás největší problémy území?", "help": "<script type=\"text/javascript\" charset=\"utf-8\">\r\n    $(document).on('ready pjax:scriptcomplete', function () {\r\n        console.log('Script initialized');\r\n\r\n        var qID = '{QID}'; // Dynamické ID otázky\r\n        var fixedAnswers = 1; // Počet odpovědí fixovaných na konec (kromě \"Jiné\")\r\n        var otherFixed = true; // Pokud má být \"Jiné\" na poslední pozici\r\n\r\n        // Najděte otázku podle ID\r\n        var $question = $('#question' + qID);\r\n        if (!$question.length) {\r\n            console.error('Otázka s ID ' + qID + ' nebyla nalezena.');\r\n            return;\r\n        }\r\n        console.log('Otázka nalezena:', $question);\r\n\r\n        // Najděte všechny odpovědi (li.answer-item)\r\n        var $answerItems = $question.find('li.answer-item');\r\n        if (!$answerItems.length) {\r\n            console.error('<PERSON><PERSON><PERSON><PERSON> odpovědi nebyly nale<PERSON>.');\r\n            return;\r\n        }\r\n        console.log('<PERSON><PERSON><PERSON><PERSON> odpo<PERSON>ědi:', $answerItems);\r\n\r\n        var $otherInput = $question.find('input[type=\"text\"]'); // \"Jiné\" input\r\n        var hasOther = $otherInput.length > 0;\r\n        var $answersList = $question.find('ul'); // Seznam odpovědí\r\n\r\n        if (!$answersList.length) {\r\n            console.error('Seznam odpovědí (ul) nebyl nalezen.');\r\n            return;\r\n        }\r\n\r\n        // Oddělte položky \"Jiné\" a fixní poslední odpověď (např. SQ009)\r\n        var $otherAnswerItem = hasOther ? $otherInput.closest('li.answer-item') : null;\r\n        var $fixedAnswerItem = $answerItems.filter('[id*=\"SQ009\"]');\r\n\r\n        // Zamíchejte odpovědi kromě těch, které jsou fixní (včetně \"Jiné\")\r\n        var $remainingItems = $answerItems.not($fixedAnswerItem).not($otherAnswerItem);\r\n        console.log('Zbývající odpovědi pro rotaci:', $remainingItems);\r\n\r\n        // Funkce pro zamíchání pole odpovědí\r\n        function shuffleArray(array) {\r\n            for (var i = array.length - 1; i > 0; i--) {\r\n                var j = Math.floor(Math.random() * (i + 1));\r\n                [array[i], array[j]] = [array[j], array[i]]; // swap\r\n            }\r\n        }\r\n\r\n        // Zamíchání odpovědí (kromě fixních)\r\n        var shuffledItems = $remainingItems.toArray();\r\n        shuffleArray(shuffledItems);\r\n\r\n        // Přesuneme zamíchané položky zpět do seznamu\r\n        $.each(shuffledItems, function(index, item) {\r\n            $(item).appendTo($answersList); // Znovu přidejte, aby se zachovalo pořadí\r\n        });\r\n\r\n        // Přesuňte fixní odpověď (např. SQ008) na předposlední místo\r\n        if ($fixedAnswerItem.length) {\r\n            console.log('Fixní odpověď k přesunutí:', $fixedAnswerItem);\r\n            $fixedAnswerItem.appendTo($answersList);\r\n        }\r\n\r\n        // Přesuňte \"Jiné\" na konec\r\n        if (otherFixed && $otherAnswerItem && $otherAnswerItem.length) {\r\n            console.log('\"Jiné\" odpověď k přesunování:', $otherAnswerItem);\r\n            $otherAnswerItem.appendTo($answersList);\r\n        }\r\n\r\n        console.log('Script execution completed.');\r\n    });\r\n</script>", "script": "", "questionl10ns": {"id": 389, "qid": 389, "question": "<PERSON><PERSON><PERSON> j<PERSON>u podle Vás největší problémy území?", "help": "<script type=\"text/javascript\" charset=\"utf-8\">\r\n    $(document).on('ready pjax:scriptcomplete', function () {\r\n        console.log('Script initialized');\r\n\r\n        var qID = '{QID}'; // Dynamické ID otázky\r\n        var fixedAnswers = 1; // Počet odpovědí fixovaných na konec (kromě \"Jiné\")\r\n        var otherFixed = true; // Pokud má být \"Jiné\" na poslední pozici\r\n\r\n        // Najděte otázku podle ID\r\n        var $question = $('#question' + qID);\r\n        if (!$question.length) {\r\n            console.error('Otázka s ID ' + qID + ' nebyla nalezena.');\r\n            return;\r\n        }\r\n        console.log('Otázka nalezena:', $question);\r\n\r\n        // Najděte všechny odpovědi (li.answer-item)\r\n        var $answerItems = $question.find('li.answer-item');\r\n        if (!$answerItems.length) {\r\n            console.error('<PERSON><PERSON><PERSON><PERSON> odpovědi nebyly nale<PERSON>.');\r\n            return;\r\n        }\r\n        console.log('<PERSON><PERSON><PERSON><PERSON> odpo<PERSON>ědi:', $answerItems);\r\n\r\n        var $otherInput = $question.find('input[type=\"text\"]'); // \"Jiné\" input\r\n        var hasOther = $otherInput.length > 0;\r\n        var $answersList = $question.find('ul'); // Seznam odpovědí\r\n\r\n        if (!$answersList.length) {\r\n            console.error('Seznam odpovědí (ul) nebyl nalezen.');\r\n            return;\r\n        }\r\n\r\n        // Oddělte položky \"Jiné\" a fixní poslední odpověď (např. SQ009)\r\n        var $otherAnswerItem = hasOther ? $otherInput.closest('li.answer-item') : null;\r\n        var $fixedAnswerItem = $answerItems.filter('[id*=\"SQ009\"]');\r\n\r\n        // Zamíchejte odpovědi kromě těch, které jsou fixní (včetně \"Jiné\")\r\n        var $remainingItems = $answerItems.not($fixedAnswerItem).not($otherAnswerItem);\r\n        console.log('Zbývající odpovědi pro rotaci:', $remainingItems);\r\n\r\n        // Funkce pro zamíchání pole odpovědí\r\n        function shuffleArray(array) {\r\n            for (var i = array.length - 1; i > 0; i--) {\r\n                var j = Math.floor(Math.random() * (i + 1));\r\n                [array[i], array[j]] = [array[j], array[i]]; // swap\r\n            }\r\n        }\r\n\r\n        // Zamíchání odpovědí (kromě fixních)\r\n        var shuffledItems = $remainingItems.toArray();\r\n        shuffleArray(shuffledItems);\r\n\r\n        // Přesuneme zamíchané položky zpět do seznamu\r\n        $.each(shuffledItems, function(index, item) {\r\n            $(item).appendTo($answersList); // Znovu přidejte, aby se zachovalo pořadí\r\n        });\r\n\r\n        // Přesuňte fixní odpověď (např. SQ008) na předposlední místo\r\n        if ($fixedAnswerItem.length) {\r\n            console.log('Fixní odpověď k přesunutí:', $fixedAnswerItem);\r\n            $fixedAnswerItem.appendTo($answersList);\r\n        }\r\n\r\n        // Přesuňte \"Jiné\" na konec\r\n        if (otherFixed && $otherAnswerItem && $otherAnswerItem.length) {\r\n            console.log('\"Jiné\" odpověď k přesunování:', $otherAnswerItem);\r\n            $otherAnswerItem.appendTo($answersList);\r\n        }\r\n\r\n        console.log('Script execution completed.');\r\n    });\r\n</script>", "script": "", "language": "cs"}, "available_answers": {"SQ009": "<PERSON><PERSON><PERSON><PERSON>, nechci odpovědět", "SQ001": "Neosvětlená nebo nepřehledná místa ", "SQ002": "<PERSON><PERSON><PERSON>, kde hrozí kolize pěších s cyklisty nebo dalšími druhy dopravy  ", "SQ003": "Zničený či chybějící mobiliář", "SQ004": "Zanedbaná zeleň", "SQ005": "Chybějící funkční vodní prvek  ", "SQ006": "Nízká kvalita povrchů cest ", "SQ007": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nebo nepřehledný orientační systém", "SQ008": "Chybějící propojení uvnitř parku chybějící nebo nedostatečná návaznost na okolí"}, "subquestions": {"404": {"title": "SQ009", "question": "<PERSON><PERSON><PERSON><PERSON>, nechci odpovědět", "scale_id": 0}, "390": {"title": "SQ001", "question": "Neosvětlená nebo nepřehledná místa ", "scale_id": 0}, "391": {"title": "SQ002", "question": "<PERSON><PERSON><PERSON>, kde hrozí kolize pěších s cyklisty nebo dalšími druhy dopravy  ", "scale_id": 0}, "392": {"title": "SQ003", "question": "Zničený či chybějící mobiliář", "scale_id": 0}, "393": {"title": "SQ004", "question": "Zanedbaná zeleň", "scale_id": 0}, "394": {"title": "SQ005", "question": "Chybějící funkční vodní prvek  ", "scale_id": 0}, "395": {"title": "SQ006", "question": "Nízká kvalita povrchů cest ", "scale_id": 0}, "396": {"title": "SQ007", "question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nebo nepřehledný orientační systém", "scale_id": 0}, "397": {"title": "SQ008", "question": "Chybějící propojení uvnitř parku chybějící nebo nedostatečná návaznost na okolí", "scale_id": 0}}, "attributes": {"array_filter": "", "array_filter_exclude": "", "array_filter_style": "0", "assessment_value": "", "cssclass": "", "display_columns": "1", "em_validation_q": "(count(self.NAOK) == 3 )|| (Q5_SQ009 == \"Y\")", "exclude_all_others": "SQ009", "exclude_all_others_auto": "0", "hidden": "0", "hide_tip": "0", "max_answers": "", "min_answers": "", "other_numbers_only": "0", "other_position": "end", "other_position_code": "", "page_break": "0", "public_statistics": "0", "random_group": "", "random_order": "0", "scale_export": "0", "statistics_graphtype": "0", "statistics_showgraph": "1"}, "attributes_lang": {"em_validation_q_tip": "{if(Q5_SQ009 != \"Y\", \"Ze seznamu níže vyberte prosím 3 problémy, které považujete za největší.\", \"\")}\r\n", "other_replace_text": "<PERSON>é, vypište:", "printable_help": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 373, "title": "SQ005", "question": "Pro venčení či cvičení psa ", "type": "T", "answers": {}, "properties": {"qid": 373, "parent_qid": 368, "sid": 415451, "gid": 14, "type": "T", "title": "SQ005", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 4, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Pro venčení či cvičení psa ", "help": null, "script": null, "questionl10ns": {"id": 373, "qid": 373, "question": "Pro venčení či cvičení psa ", "help": null, "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 394, "title": "SQ005", "question": "Chybějící funkční vodní prvek  ", "type": "T", "answers": {}, "properties": {"qid": 394, "parent_qid": 389, "sid": 415451, "gid": 14, "type": "T", "title": "SQ005", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 4, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Chybějící funkční vodní prvek  ", "help": "", "script": null, "questionl10ns": {"id": 394, "qid": 394, "question": "Chybějící funkční vodní prvek  ", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 386, "title": "SQ006", "question": "Zázemí pro sport a pohybové aktivity", "type": "T", "answers": {}, "properties": {"qid": 386, "parent_qid": 379, "sid": 415451, "gid": 14, "type": "T", "title": "SQ006", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 5, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Zázemí pro sport a pohybové aktivity", "help": "", "script": null, "questionl10ns": {"id": 386, "qid": 386, "question": "Zázemí pro sport a pohybové aktivity", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 374, "title": "SQ006", "question": "<PERSON><PERSON><PERSON><PERSON>", "type": "T", "answers": {}, "properties": {"qid": 374, "parent_qid": 368, "sid": 415451, "gid": 14, "type": "T", "title": "SQ006", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 5, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "<PERSON><PERSON><PERSON><PERSON>", "help": null, "script": null, "questionl10ns": {"id": 374, "qid": 374, "question": "<PERSON><PERSON><PERSON><PERSON>", "help": null, "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 395, "title": "SQ006", "question": "Nízká kvalita povrchů cest ", "type": "T", "answers": {}, "properties": {"qid": 395, "parent_qid": 389, "sid": 415451, "gid": 14, "type": "T", "title": "SQ006", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 5, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Nízká kvalita povrchů cest ", "help": "", "script": null, "questionl10ns": {"id": 395, "qid": 395, "question": "Nízká kvalita povrchů cest ", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 398, "title": "Q6", "question": "<PERSON> něco, co byste chtě<PERSON> dodat nebo sd<PERSON><PERSON> budoucím autorům projektu obnovy Chotkových sadů, a na co v této anketě nebyl prostor?", "type": "X", "answers": {}, "properties": {"qid": 398, "parent_qid": 0, "sid": 415451, "gid": 14, "type": "X", "title": "Q6", "preg": null, "other": "N", "mandatory": "N", "encrypted": "N", "question_order": 5, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": "boilerplate", "modulename": "", "same_script": 0, "question": "<PERSON> něco, co byste chtě<PERSON> dodat nebo sd<PERSON><PERSON> budoucím autorům projektu obnovy Chotkových sadů, a na co v této anketě nebyl prostor?", "help": "", "script": "", "questionl10ns": {"id": 398, "qid": 398, "question": "<PERSON> něco, co byste chtě<PERSON> dodat nebo sd<PERSON><PERSON> budoucím autorům projektu obnovy Chotkových sadů, a na co v této anketě nebyl prostor?", "help": "", "script": "", "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "hidden": "0", "hide_tip": "0", "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 375, "title": "SQ007", "question": "Pro trávení času s dětmi", "type": "T", "answers": {}, "properties": {"qid": 375, "parent_qid": 368, "sid": 415451, "gid": 14, "type": "T", "title": "SQ007", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 6, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Pro trávení času s dětmi", "help": null, "script": null, "questionl10ns": {"id": 375, "qid": 375, "question": "Pro trávení času s dětmi", "help": null, "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 387, "title": "SQ007", "question": "Návaznost na okolí", "type": "T", "answers": {}, "properties": {"qid": 387, "parent_qid": 379, "sid": 415451, "gid": 14, "type": "T", "title": "SQ007", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 6, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Návaznost na okolí", "help": "", "script": null, "questionl10ns": {"id": 387, "qid": 387, "question": "Návaznost na okolí", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 396, "title": "SQ007", "question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nebo nepřehledný orientační systém", "type": "T", "answers": {}, "properties": {"qid": 396, "parent_qid": 389, "sid": 415451, "gid": 14, "type": "T", "title": "SQ007", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 6, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nebo nepřehledný orientační systém", "help": "", "script": null, "questionl10ns": {"id": 396, "qid": 396, "question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, nebo nepřehledný orientační systém", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 399, "title": "Q6a", "question": "Vypište prosím:", "type": "T", "answers": {}, "properties": {"qid": 399, "parent_qid": 0, "sid": 415451, "gid": 14, "type": "T", "title": "Q6a", "preg": "", "other": "N", "mandatory": "N", "encrypted": "N", "question_order": 6, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": "longfreetext", "modulename": "", "same_script": 0, "question": "Vypište prosím:", "help": "", "script": "", "questionl10ns": {"id": 399, "qid": 399, "question": "Vypište prosím:", "help": "", "script": "", "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": "", "maximum_chars": "", "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": "", "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 376, "title": "SQ008", "question": "Pro sportovní aktivity ve skupině", "type": "T", "answers": {}, "properties": {"qid": 376, "parent_qid": 368, "sid": 415451, "gid": 14, "type": "T", "title": "SQ008", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 7, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Pro sportovní aktivity ve skupině", "help": null, "script": null, "questionl10ns": {"id": 376, "qid": 376, "question": "Pro sportovní aktivity ve skupině", "help": null, "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 388, "title": "SQ008", "question": "<PERSON><PERSON><PERSON><PERSON>, nechci odpovědět", "type": "T", "answers": {}, "properties": {"qid": 388, "parent_qid": 379, "sid": 415451, "gid": 14, "type": "T", "title": "SQ008", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 7, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "<PERSON><PERSON><PERSON><PERSON>, nechci odpovědět", "help": "", "script": null, "questionl10ns": {"id": 388, "qid": 388, "question": "<PERSON><PERSON><PERSON><PERSON>, nechci odpovědět", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 397, "title": "SQ008", "question": "Chybějící propojení uvnitř parku chybějící nebo nedostatečná návaznost na okolí", "type": "T", "answers": {}, "properties": {"qid": 397, "parent_qid": 389, "sid": 415451, "gid": 14, "type": "T", "title": "SQ008", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 7, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "Chybějící propojení uvnitř parku chybějící nebo nedostatečná návaznost na okolí", "help": "", "script": null, "questionl10ns": {"id": 397, "qid": 397, "question": "Chybějící propojení uvnitř parku chybějící nebo nedostatečná návaznost na okolí", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 400, "title": "Q6b", "question": "A ještě něco? Vypište prosím:", "type": "T", "answers": {}, "properties": {"qid": 400, "parent_qid": 0, "sid": 415451, "gid": 14, "type": "T", "title": "Q6b", "preg": "", "other": "N", "mandatory": "N", "encrypted": "N", "question_order": 7, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": "longfreetext", "modulename": "", "same_script": 0, "question": "A ještě něco? Vypište prosím:", "help": "", "script": "", "questionl10ns": {"id": 400, "qid": 400, "question": "A ještě něco? Vypište prosím:", "help": "", "script": "", "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": "", "maximum_chars": "", "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": "", "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 404, "title": "SQ009", "question": "<PERSON><PERSON><PERSON><PERSON>, nechci odpovědět", "type": "T", "answers": {}, "properties": {"qid": 404, "parent_qid": 389, "sid": 415451, "gid": 14, "type": "T", "title": "SQ009", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 8, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "<PERSON><PERSON><PERSON><PERSON>, nechci odpovědět", "help": "", "script": null, "questionl10ns": {"id": 404, "qid": 404, "question": "<PERSON><PERSON><PERSON><PERSON>, nechci odpovědět", "help": "", "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}, {"qid": 377, "title": "SQ009", "question": "K něčemu jinému, vypište", "type": "T", "answers": {}, "properties": {"qid": 377, "parent_qid": 368, "sid": 415451, "gid": 14, "type": "T", "title": "SQ009", "preg": null, "other": "N", "mandatory": null, "encrypted": "N", "question_order": 8, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": null, "modulename": null, "same_script": 0, "question": "K něčemu jinému, vypište", "help": null, "script": null, "questionl10ns": {"id": 377, "qid": 377, "question": "K něčemu jinému, vypište", "help": null, "script": null, "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"cssclass": "", "display_rows": "", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "input_size": null, "maximum_chars": null, "page_break": "0", "random_group": "", "statistics_graphtype": "0", "statistics_showgraph": "1", "text_input_width": null, "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": "No available answer options", "defaultvalue": null}}]}, {"gid": 15, "group_name": "Socio-demo", "questions": [{"qid": 401, "title": "Q7", "question": "J<PERSON>é je vaše p<PERSON>?", "type": "L", "answers": {}, "properties": {"qid": 401, "parent_qid": 0, "sid": 415451, "gid": 15, "type": "L", "title": "Q7", "preg": null, "other": "N", "mandatory": "Y", "encrypted": "N", "question_order": 1, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": "listradio", "modulename": "", "same_script": 0, "question": "J<PERSON>é je vaše p<PERSON>?", "help": "", "script": "", "questionl10ns": {"id": 401, "qid": 401, "question": "J<PERSON>é je vaše p<PERSON>?", "help": "", "script": "", "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"answer_order": "normal", "array_filter": "", "array_filter_exclude": "", "array_filter_style": "0", "cssclass": "", "display_columns": "3", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "other_comment_mandatory": "0", "other_numbers_only": "0", "other_position": "default", "other_position_code": "", "page_break": "0", "public_statistics": "0", "random_group": "", "scale_export": "0", "statistics_graphtype": "0", "statistics_showgraph": "1", "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "other_replace_text": "", "printable_help": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": {"AO01": {"answer": "Muž", "assessment_value": 0, "scale_id": 0, "order": 0}, "AO02": {"answer": "Ž<PERSON>", "assessment_value": 0, "scale_id": 0, "order": 1}, "AO03": {"answer": "<PERSON><PERSON>", "assessment_value": 0, "scale_id": 0, "order": 2}}, "defaultvalue": null}}, {"qid": 402, "title": "Q8", "question": "<PERSON><PERSON> je vám let?", "type": "L", "answers": {}, "properties": {"qid": 402, "parent_qid": 0, "sid": 415451, "gid": 15, "type": "L", "title": "Q8", "preg": null, "other": "N", "mandatory": "Y", "encrypted": "N", "question_order": 2, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": "listradio", "modulename": "", "same_script": 0, "question": "<PERSON><PERSON> je vám let?", "help": "", "script": "", "questionl10ns": {"id": 402, "qid": 402, "question": "<PERSON><PERSON> je vám let?", "help": "", "script": "", "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"answer_order": "normal", "array_filter": "", "array_filter_exclude": "", "array_filter_style": "0", "cssclass": "", "display_columns": "4", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "other_comment_mandatory": "0", "other_numbers_only": "0", "other_position": "default", "other_position_code": "", "page_break": "0", "public_statistics": "0", "random_group": "", "scale_export": "0", "statistics_graphtype": "0", "statistics_showgraph": "1", "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "other_replace_text": "", "printable_help": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": {"AO01": {"answer": "méně než 18 let", "assessment_value": 0, "scale_id": 0, "order": 0}, "AO02": {"answer": "18–24 let", "assessment_value": 0, "scale_id": 0, "order": 1}, "AO03": {"answer": "25–34 let", "assessment_value": 0, "scale_id": 0, "order": 2}, "AO04": {"answer": "35–44 let", "assessment_value": 0, "scale_id": 0, "order": 3}, "AO05": {"answer": "45–54 let", "assessment_value": 0, "scale_id": 0, "order": 4}, "AO06": {"answer": "55–64 let", "assessment_value": 0, "scale_id": 0, "order": 5}, "AO07": {"answer": "65 a více let", "assessment_value": 0, "scale_id": 0, "order": 6}}, "defaultvalue": null}}, {"qid": 403, "title": "Q9", "question": "Kde aktuálně žijete?", "type": "L", "answers": {}, "properties": {"qid": 403, "parent_qid": 0, "sid": 415451, "gid": 15, "type": "L", "title": "Q9", "preg": null, "other": "N", "mandatory": "Y", "encrypted": "N", "question_order": 3, "scale_id": 0, "same_default": 0, "relevance": "1", "question_theme_name": "listradio", "modulename": "", "same_script": 0, "question": "Kde aktuálně žijete?", "help": "", "script": "", "questionl10ns": {"id": 403, "qid": 403, "question": "Kde aktuálně žijete?", "help": "", "script": "", "language": "cs"}, "available_answers": "No available answers", "subquestions": "No available answers", "attributes": {"answer_order": "normal", "array_filter": "", "array_filter_exclude": "", "array_filter_style": "0", "cssclass": "", "display_columns": "5", "em_validation_q": "", "hidden": "0", "hide_tip": "0", "other_comment_mandatory": "0", "other_numbers_only": "0", "other_position": "default", "other_position_code": "", "page_break": "0", "public_statistics": "0", "random_group": "", "scale_export": "0", "statistics_graphtype": "0", "statistics_showgraph": "1", "time_limit": "", "time_limit_action": "1", "time_limit_disable_next": "0", "time_limit_disable_prev": "0", "time_limit_message_delay": "", "time_limit_message_style": "", "time_limit_timer_style": "", "time_limit_warning": "", "time_limit_warning_2": "", "time_limit_warning_2_display_time": "", "time_limit_warning_2_style": "", "time_limit_warning_display_time": "", "time_limit_warning_style": ""}, "attributes_lang": {"em_validation_q_tip": "", "other_replace_text": "", "printable_help": "", "time_limit_countdown_message": "", "time_limit_message": "", "time_limit_warning_2_message": "", "time_limit_warning_message": ""}, "answeroptions": {"AO01": {"answer": "Praha 1", "assessment_value": 0, "scale_id": 0, "order": 0}, "AO02": {"answer": "Praha 6", "assessment_value": 0, "scale_id": 0, "order": 1}, "AO03": {"answer": "Praha 7", "assessment_value": 0, "scale_id": 0, "order": 2}, "AO04": {"answer": "<PERSON><PERSON> p<PERSON> městská část", "assessment_value": 0, "scale_id": 0, "order": 3}, "AO05": {"answer": "<PERSON><PERSON>", "assessment_value": 0, "scale_id": 0, "order": 4}}, "defaultvalue": null}}]}], "questions": [], "timestamp": "2025-01-13T01:08:53.965564"}