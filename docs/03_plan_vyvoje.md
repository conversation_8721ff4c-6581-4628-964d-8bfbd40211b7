# Plán vývoje

## Fáze 1: Základní infrastruktura (1 týden)
1. Nastavení projektu
   - [x] Inicializace Git repozitáře
   - [x] Vytvoření základní struktury projektu
   - [ ] Konfigurace CI/CD pipeline

2. Implementace API Gateway
   - [ ] Základní třída pro komunikaci s API
   - [ ] Implementace autentizace
   - [ ] Přidání cachování

## Fáze 2: Datový pipeline (2 týdny)
1. <PERSON>ištěn<PERSON> dat
   - [ ] Implementace základních transformací
   - [ ] Validace datových typů
   - [ ] Logování chyb

2. Transformace dat
   - [ ] Převedení do long formátu
   - [ ] Agregace dat
   - [ ] Přejmenování sloupců

## Fáze 3: GUI a integrace (3 týdny)
1. Uživatelské rozhraní
   - [ ] <PERSON><PERSON><PERSON><PERSON> okno aplikace
   - [ ] Výb<PERSON>r průzkumu
   - [ ] Zobrazení metadat

2. Integrace s Datawrapper
   - [ ] Generování základn<PERSON>ch grafů
   - [ ] Nastavení metadat grafu
   - [ ] Export výsledků

## Metodika sledování postupu

- **K implementaci:** Úkol čeká na zahájení
- **V implementaci:** Úkol je v procesu řešení
- **K testování:** Úkol čeká na ověření
- **Hotovo:** Úkol byl dokončen a ověřen

Pro sledování použijeme GitHub Issues s příslušnými labely.
