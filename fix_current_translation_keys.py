#!/usr/bin/env python3
"""
Oprava klíčů v translations_en-US.json podle aktuálního chart_data.json
"""

import sys
import os
sys.path.append('src')

import json

def fix_current_translation_keys():
    """Oprava klíčů podle aktuálního chart_data.json"""
    
    survey_id = "827822"
    chart_data_path = f"src/data/{survey_id}/chart_data.json"
    translations_path = f"src/data/{survey_id}/translations_en-US.json"
    
    print("🔧 Oprava klíčů podle aktuálního chart_data.json")
    print("=" * 60)
    
    # Načtení aktuálních dat
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        chart_data = json.load(f)
    
    with open(translations_path, 'r', encoding='utf-8') as f:
        translations = json.load(f)
    
    # <PERSON>ískán<PERSON> aktuálních názvů z chart_data
    current_names = [item.get('name', '') for item in chart_data]
    print(f"📊 Aktuální názvy v chart_data:")
    for i, name in enumerate(current_names[:10]):
        print(f"   [{i+1}]: '{name}'")
    
    # Vytvoření nového mapování podle aktuálních názvů
    new_question_names = {}
    
    # Mapování aktuálních názvů na anglické překlady
    name_mapping = {
        "Typ prohlížeče": "Browser Type",
        "Zastupovaná instituce/organizace": "Represented Institution/Organization",
        "Právní forma zúčastněných organizací": "Legal Form of Participating Organizations",
        "Hlavní tematické oblasti působení organizací": "Main Thematic Areas of Organizations",
        "Fáze zapojení do legislativního procesu (EU a ČR)": "Phases of Involvement in the Legislative Process (EU & CZ)",
        "Informační kanály o konzultacích k legislativě EU": "Information Channels for EU Legislation Consultations",
        "Relevance informací z komunikačních kanálů": "Relevance of Information from Communication Channels",
        "Informační kanály o implementaci legislativy EU v ČR": "Information Channels for EU Legislation Implementation in the Czech Republic",
        "Míra vysoké spokojenosti se zapojením do legislativy": "Rate of High Satisfaction with Legislative Involvement",
        "Míra vysoké nespokojenosti se zapojením do legislativy": "Rate of High Dissatisfaction with Legislative Involvement",
        "Míra ztotožnění s uvedenými názory": "Level of Agreement with Stated Opinions",
        "Postrádané informační kanály o legislativním procesu": "Missing Information Channels on the Legislative Process",
        "Zájem o zaslání výsledků průzkumu": "Interest in Receiving Survey Results",
        "Zájem o zapojení do konzultace výstupů projektu": "Interest in Consulting on Project's Final Outputs",
        "Kontaktní e-mail": "Contact E-mail",
        "Specifikace odpovědi 'Jiným způsobem'": "Specification of 'In another way' response",
        "Specifikace odpovědi 'Jinak'": "Specification of 'Other' response"
    }
    
    # Aplikace mapování na aktuální názvy
    mapped_count = 0
    for czech_name in current_names:
        if czech_name in name_mapping:
            new_question_names[czech_name] = name_mapping[czech_name]
            print(f"   ✅ '{czech_name}' -> '{name_mapping[czech_name]}'")
            mapped_count += 1
        else:
            print(f"   ❌ Nenalezeno mapování pro: '{czech_name}'")
    
    # Aktualizace překladů
    translations['question_names'] = new_question_names
    
    # Uložení opravených překladů
    with open(translations_path, 'w', encoding='utf-8') as f:
        json.dump(translations, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ Opraveno {mapped_count}/{len(current_names)} klíčů")
    print(f"📁 Uloženo do: {translations_path}")

if __name__ == "__main__":
    fix_current_translation_keys()
