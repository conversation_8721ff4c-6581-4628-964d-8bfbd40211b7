#!/usr/bin/env python3
"""
Oprava českého souboru - přeznačení personal_data jako safe_to_translate
"""

import sys
import os
sys.path.append('src')

import json

def fix_czech_personal_data():
    """Oprava českého souboru - přesun personal_data do safe_to_translate"""
    
    survey_id = "827822"
    czech_path = f"src/data/{survey_id}/translations_cs-CZ.json"
    
    print("🔧 Oprava českého souboru - přeznačení personal_data")
    print("=" * 60)
    
    # Načtení českého souboru
    with open(czech_path, 'r', encoding='utf-8') as f:
        czech_data = json.load(f)
    
    # Kontrola free_text_responses
    free_text = czech_data.get('free_text_responses', {}).get('by_question', {})
    
    total_moved = 0
    
    for question_code, question_data in free_text.items():
        personal_data = question_data.get('personal_data', {})
        safe_to_translate = question_data.get('safe_to_translate', {})
        
        if personal_data:
            print(f"\n📋 Otázka {question_code}:")
            print(f"   Personal data: {len(personal_data)} položek")
            print(f"   Safe to translate: {len(safe_to_translate)} položek")
            
            # Přesun všech personal_data do safe_to_translate
            for key, value in personal_data.items():
                if key not in safe_to_translate:
                    safe_to_translate[key] = value
                    total_moved += 1
                    print(f"   ✅ Přesunuto: '{key[:50]}...'")
            
            # Vyčištění personal_data
            question_data['personal_data'] = {}
            question_data['safe_to_translate'] = safe_to_translate
    
    # Uložení opravených dat
    with open(czech_path, 'w', encoding='utf-8') as f:
        json.dump(czech_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ Celkem přesunuto: {total_moved} položek")
    print(f"📁 Uloženo do: {czech_path}")

if __name__ == "__main__":
    fix_czech_personal_data()
