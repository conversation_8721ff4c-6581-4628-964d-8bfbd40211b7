#!/usr/bin/env python3
"""
Automatické generování anglické verze ze české
"""

import sys
import os
sys.path.append('src')

import json

def generate_english_from_czech():
    """Generování anglické verze ze české"""
    
    survey_id = "827822"
    czech_path = f"src/data/{survey_id}/translations_cs-CZ.json"
    english_path = f"src/data/{survey_id}/translations_en-US.json"
    
    print("🇺🇸 Generování anglické verze ze české")
    print("=" * 60)
    
    # Načtení českého souboru
    with open(czech_path, 'r', encoding='utf-8') as f:
        czech_data = json.load(f)
    
    # Vytvoření anglické struktury
    english_data = {
        "metadata": {
            "survey_id": survey_id,
            "language": "en-US",
            "created": "auto-generated-from-czech",
            "version": "3.0",
            "structure": "categorized_for_llm_safety"
        },
        "language_settings": {
            "chart_language": "en-US",
            "available_languages": {
                "cs-CZ": "Čeština",
                "en-US": "English (US)",
                "en-GB": "English (UK)",
                "de-DE": "Deutsch",
                "fr-FR": "Français",
                "es-ES": "Español"
            }
        },
        "question_names": {},
        "subquestions": {},
        "scale_responses": {},
        "choice_responses": {},
        "free_text_responses": {"by_question": {}},
        "chart_titles": {}
    }
    
    # Mapování českých názvů na anglické
    czech_to_english = {
        # Question names
        "Typ prohlížeče (technický údaj)": "Browser Type (Technical Data)",
        "Zastupovaná instituce/organizace": "Represented Institution/Organization",
        "Právní forma zúčastněných organizací": "Legal Form of Participating Organizations",
        "Hlavní tematické oblasti působení organizací": "Main Thematic Areas of Organizations",
        "Fáze zapojení do legislativního procesu (EU a ČR)": "Phases of Involvement in the Legislative Process (EU & CZ)",
        "Informační kanály o konzultacích k legislativě EU": "Information Channels for EU Legislation Consultations",
        "Relevance informací z komunikačních kanálů": "Relevance of Information from Communication Channels",
        "Informační kanály o implementaci legislativy EU v ČR": "Information Channels for EU Legislation Implementation in the Czech Republic",
        "Míra vysoké spokojenosti se zapojením do legislativy": "Rate of High Satisfaction with Legislative Involvement",
        "Míra vysoké nespokojenosti se zapojením do legislativy": "Rate of High Dissatisfaction with Legislative Involvement",
        "Míra ztotožnění s uvedenými názory": "Level of Agreement with Stated Opinions",
        "Postrádané informační kanály o legislativním procesu": "Missing Information Channels on the Legislative Process",
        "Zájem o zaslání výsledků průzkumu": "Interest in Receiving Survey Results",
        "Zájem o zapojení do konzultace výstupů projektu": "Interest in Consulting on Project's Final Outputs",
        "Kontaktní e-mail": "Contact E-mail",
        "Specifikace odpovědi 'Jiným způsobem'": "Specification of 'In another way' response",
        "Specifikace odpovědi 'Jinak'": "Specification of 'Other' response",
        
        # Subquestions
        "Při tvoření obsahového rámce návrhu": "During the creation of the content framework of the proposal",
        "Při přípravě legislativního návrhu Evropskou komisí": "During the preparation of the legislative proposal by the European Commission",
        "Při tvorbě národní pozice (oficiálního stanoviska ČR k legislativnímu návrhu)": "During the creation of the national position (the Czech Republic's official stance on the legislative proposal)",
        "Ve fázi implementace schválené evropské legislativy do českého právního řádu": "During the implementation phase of approved European legislation into Czech law",
        "Ani v jedné z výše uvedených fází": "In none of the above phases",
        
        # Scale responses
        "rozhodně ano": "definitely yes",
        "spíše ano": "rather yes", 
        "spíše ne": "rather no",
        "rozhodně ne": "definitely no",
        "neumím to posoudit": "I cannot assess this",
        "velmi relevantní": "very relevant",
        "převážně relevantní": "mostly relevant",
        "málo relevantní": "little relevant",
        "nerelevantní": "not relevant",
        "vždy": "always",
        "skoro vždy": "almost always",
        "ve většině případů": "in most cases",
        "v menšině případů": "in minority of cases",
        "jen výjimečně": "only exceptionally",
        "nikdy": "never",
        
        # Choice responses
        "Ano": "Yes",
        "Ne": "No"
    }
    
    # Překlad question_names
    czech_questions = czech_data.get('question_names', {})
    for czech_key, czech_value in czech_questions.items():
        english_key = czech_to_english.get(czech_key, czech_key)
        english_value = czech_to_english.get(czech_value, czech_value)
        english_data['question_names'][english_key] = english_value
    
    print(f"✅ Přeloženo {len(english_data['question_names'])} question_names")
    
    # Překlad subquestions
    czech_subquestions = czech_data.get('subquestions', {})
    for czech_key, czech_value in czech_subquestions.items():
        english_key = czech_to_english.get(czech_key, czech_key)
        english_value = czech_to_english.get(czech_value, czech_value)
        english_data['subquestions'][english_key] = english_value
    
    print(f"✅ Přeloženo {len(english_data['subquestions'])} subquestions")
    
    # Překlad scale_responses
    czech_scale = czech_data.get('scale_responses', {})
    for czech_key, czech_value in czech_scale.items():
        english_key = czech_to_english.get(czech_key, czech_key)
        english_value = czech_to_english.get(czech_value, czech_value)
        english_data['scale_responses'][english_key] = english_value
    
    print(f"✅ Přeloženo {len(english_data['scale_responses'])} scale_responses")
    
    # Překlad choice_responses
    czech_choice = czech_data.get('choice_responses', {})
    for czech_key, czech_value in czech_choice.items():
        english_key = czech_to_english.get(czech_key, czech_key)
        english_value = czech_to_english.get(czech_value, czech_value)
        english_data['choice_responses'][english_key] = english_value
    
    print(f"✅ Přeloženo {len(english_data['choice_responses'])} choice_responses")
    
    # Překlad free_text_responses (jen safe_to_translate)
    czech_free_text = czech_data.get('free_text_responses', {}).get('by_question', {})
    total_free_text = 0
    
    for question_code, question_data in czech_free_text.items():
        english_data['free_text_responses']['by_question'][question_code] = {
            "safe_to_translate": {},
            "personal_data": {}
        }
        
        # Překlad safe_to_translate
        safe_texts = question_data.get('safe_to_translate', {})
        for czech_text, czech_translation in safe_texts.items():
            # Pro free text používáme původní text jako klíč a překlad jako hodnotu
            english_translation = f"[EN] {czech_translation}"  # Označíme jako anglický překlad
            english_data['free_text_responses']['by_question'][question_code]['safe_to_translate'][czech_text] = english_translation
            total_free_text += 1
    
    print(f"✅ Přeloženo {total_free_text} free_text_responses")
    
    # Uložení anglického souboru
    with open(english_path, 'w', encoding='utf-8') as f:
        json.dump(english_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 Anglická verze vygenerována!")
    print(f"📁 Uloženo do: {english_path}")
    print(f"📊 Celkem položek:")
    print(f"   Question names: {len(english_data['question_names'])}")
    print(f"   Subquestions: {len(english_data['subquestions'])}")
    print(f"   Scale responses: {len(english_data['scale_responses'])}")
    print(f"   Choice responses: {len(english_data['choice_responses'])}")
    print(f"   Free text responses: {total_free_text}")

if __name__ == "__main__":
    generate_english_from_czech()
