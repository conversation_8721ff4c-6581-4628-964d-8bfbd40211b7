import os
try:
    from dotenv import load_dotenv
except ImportError:
    def load_dotenv():
        pass

def load_config():
    load_dotenv()
    return {
        'DEFAULT_CHART_TYPE': os.getenv('DEFAULT_CHART_TYPE', 'bar_chart'),
        'GRAPH_FOOTER': os.getenv('GRAPH_FOOTER', 'Data z LimeSurvey'),
        'EXPORT_PNG': os.getenv('EXPORT_PNG', 'true').lower() == 'true',
        'charts_root': os.getenv('CHARTS_ROOT', 'charts'),
        'DATAWRAPPER_TEAM_ID': os.getenv('DATAWRAPPER_TEAM_ID'),
        'DATAWRAPPER_LIMESURVEY_FOLDER_ID': os.getenv('DATAWRAPPER_LIMESURVEY_FOLDER_ID'),
        'LIMESURVEY': {
            'API_URL': os.getenv('LIMESURVEY_API_URL'),
            'USERNAME': os.getenv('LIMESURVEY_USERNAME'),
            'PASSWORD': os.getenv('LIMESURVEY_PASSWORD')
        }
    }
