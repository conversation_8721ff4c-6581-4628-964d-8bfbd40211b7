# removed misplaced future import
try:
    import pandas as pd
except ImportError:
    pd = None
import base64
import csv
from io import StringIO
from typing import Dict, List, Optional, Any
from logger import get_logger
from datetime import datetime

logger = get_logger(__name__)

class DataTransformer:
    def __init__(self, mapping_file: Optional[str] = None):
        self.mapping_file = mapping_file
        self.logger = get_logger(__name__)

    def transform_responses(self, input_path: str):
        """Transformace odpovědí do long formátu"""
        if pd is None:
            raise ImportError("pandas library is required for data transformation. Please install pandas")
        # redundant future import removed
        try:
            if not self.mapping_file:
                raise ValueError("Mapping file is required")
                
            df = pd.read_csv(input_path, sep=';')
            mapping_df = pd.read_csv(self.mapping_file)
            
            # Vytvoření long formátu
            question_cols = mapping_df['code'].tolist()
            id_cols = [col for col in df.columns if col not in question_cols]
            
            long_df = pd.melt(df, 
                             id_vars=id_cols,
                             value_vars=question_cols,
                             var_name='question_code',
                             value_name='response')
            
            # Připojení názvů otázek
            long_df = long_df.merge(mapping_df[['code', 'original_name', 'is_main_question']], 
                                   left_on='question_code',
                                   right_on='code',
                                   how='left')
            
            return long_df.drop('code', axis=1)
            
        except Exception as e:
            self.logger.error(f"Chyba při transformaci odpovědí: {str(e)}")
            raise

    def prepare_chart_data(self, df, question_type: Optional[str] = None) -> Dict:
        """Příprava dat pro graf"""
        try:
            if question_type not in ['single_choice', 'multiple_choice', 'numeric']:
                question_type = 'single_choice'
                
            chart_data = {
                'chart_type': question_type,
                'data': []
            }
            
            # Agregace dat podle typu otázky
            if question_type == 'single_choice':
                response_counts = df['response'].value_counts()
                for response, count in response_counts.items():
                    chart_data['data'].append({
                        'label': response,
                        'value': int(count)
                    })
                    
            return chart_data
            
        except Exception as e:
            self.logger.error(f"Chyba při přípravě dat pro graf: {str(e)}")
            raise

    def save_transformed_data(self, df, output_path: str) -> None:
        """Uložení transformovaných dat"""
        try:
            df.to_csv(output_path, index=False)
            self.logger.info(f"Data uložena do {output_path}")
        except Exception as e:
            self.logger.error(f"Chyba při ukládání dat: {str(e)}")
            raise

    def generate_mapping_file(self, lss_path: str, output_path: str) -> None:
        """Generování mapovacího souboru"""
        try:
            generate_question_mapping(self.mapping_file, lss_path, output_path)
            self.logger.info(f"Mapovací soubor vygenerován: {output_path}")
        except Exception as e:
            self.logger.error(f"Chyba při generování mapování: {str(e)}")
            raise

def decompress_csv(file_path: str) -> None:
    """Dekomprese binárního CSV na textový formát"""
    try:
        with open(file_path, 'rb') as f:
            content = f.read()
            
        # Dekódování z base64
        decoded = base64.b64decode(content).decode('utf-8')
        
        # Odstranění BOM znaku
        fixed_content = decoded.lstrip('\ufeff')
        
        # Zpracování CSV po řádcích
        rows = []
        reader = csv.reader(StringIO(fixed_content), delimiter=';', quotechar='"')
        
        for row in reader:
            processed_row = []
            for field in row:
                if field == 'N/A' or not field.strip():
                    processed_row.append('""')
                else:
                    # Zachování přesného formátu textu
                    field = field.strip()
                    # Nahrazení různých typů zalomení řádků za \n
                    field = field.replace('\r\n', '\n').replace('\r', '\n')
                    # Escapování uvozovek a obalení pole
                    if '"' in field or '\n' in field or ';' in field:
                        field = f'"{field.replace('"', '""')}"'
                    else:
                        field = f'"{field}"'
                    processed_row.append(field)
            rows.append(';'.join(processed_row))
        
        # Přepsání opraveného obsahu
        with open(file_path, 'w', encoding='utf-8', newline='') as f:
            f.write('\n'.join(rows))
            
        logger.info(f"Soubor {file_path} úspěšně dekomprimován")
        
    except Exception as e:
        logger.error(f"Chyba při dekompresi CSV: {str(e)}")

def strip_html(text: str) -> str:
    """Odstranění HTML značek z textu"""
    from html.parser import HTMLParser
    
    class MLStripper(HTMLParser):
        def __init__(self):
            super().__init__()
            self.reset()
            self.strict = False
            self.convert_charrefs = True
            self.text = []
            
        def handle_data(self, d):
            self.text.append(d)
            
        def get_data(self):
            return ''.join(self.text)
    
    s = MLStripper()
    s.feed(text)
    return s.get_data().strip()

def generate_question_mapping(csv_path: str, lss_path: str, output_path: str) -> bool:
    """Generování mapování otázek z CSV a LSS souborů s podporou otázek typu pole"""
    try:
        import json
        
        # Načtení hlaviček z CSV
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f, delimiter=';')
            headers = next(reader)
            
        # Filtrace pouze otázek (začínají na G)
        question_codes = [h for h in headers if h.startswith('G')]
        
        # Načtení a parsování LSS souboru
        with open(lss_path, 'r', encoding='utf-8') as f:
            lss_data = json.load(f)
            
        # Vytvoření slovníku otázek s fallback hodnotami
        question_names = {code: code for code in question_codes}
        question_texts = {code: code for code in question_codes}
        
        # Doplnění skutečných názvů z LSS
        for group in lss_data['groups']:
            for question in group['questions']:
                # Použití title z otázky přímo
                code = question['title']
                question_text = strip_html(question['question'])
                
                if code in question_codes:
                    question_names[code] = question_text
                    question_texts[code] = question_text
                
                # Přidání hlavní otázky typu pole (pokud má subotázky)
                has_subquestions = False
                
                # Zkontroluj subotázky v properties
                if 'properties' in question and isinstance(question['properties'], dict):
                    properties = question['properties']
                    if 'subquestions' in properties and isinstance(properties['subquestions'], dict):
                        has_subquestions = True
                
                # Zkontroluj subotázky přímo v otázce
                if 'subquestions' in question and question['subquestions']:
                    has_subquestions = True
                
                # Přidej hlavní otázku pokud má subotázky a je typu pole
                if has_subquestions and question.get('type') in ['F', 'H', '1']:
                    # Přidej hlavní otázku do question_codes pokud tam není
                    if code not in question_codes:
                        question_codes.append(code)
                        question_names[code] = question_text
                        question_texts[code] = question_text
                        logger.info(f"Přidána hlavní otázka pole: {code}")
                
                # Zpracování otázek typu pole (F) - data jsou v properties
                if 'properties' in question and isinstance(question['properties'], dict):
                    properties = question['properties']
                    
                    # Subotázky z properties.subquestions
                    if 'subquestions' in properties and isinstance(properties['subquestions'], dict):
                        for sub_qid, sub_data in properties['subquestions'].items():
                            sub_code = sub_data.get('title', '')
                            sub_text = strip_html(sub_data.get('question', ''))
                            subcode = f"{code}[{sub_code}]"
                            
                            if subcode in question_codes:
                                question_names[subcode] = sub_text
                                question_texts[subcode] = sub_text
                    
                    # Subotázky z properties.available_answers (starší formát)
                    if ('available_answers' in properties and
                        isinstance(properties['available_answers'], dict)):
                        for sub_code, sub_text in properties['available_answers'].items():
                            subcode = f"{code}[{sub_code}]"
                            if subcode in question_codes:
                                question_names[subcode] = strip_html(sub_text)
                                question_texts[subcode] = strip_html(sub_text)
        
        # Generování výstupního CSV s question_text sloupcem
        with open(output_path, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['question_code', 'question_name', 'question_text', 'is_main_question'])
            
            for code in question_codes:
                # Rozlišení hlavních otázek a subotázek
                is_main = '[' not in code
                writer.writerow([code, question_names[code], question_texts[code], is_main])
                
        logger.info(f"Vygenerováno mapování pro {len(question_codes)} otázek")
        return True
        
    except Exception as e:
        logger.error(f"Chyba při generování mapování otázek: {str(e)}")
        return False

def transform_to_long_format(csv_path: str, mapping_path: str, output_path: str) -> bool:
    """Transformace CSV dat na long formát"""
    if pd is None:
        logger.error("Pandas knihovna není dostupná. Pro transformaci dat je potřeba nainstalovat pandas.")
        return False
        
    try:
        # Načtení dat
        df = pd.read_csv(csv_path, sep=';')
        mapping_df = pd.read_csv(mapping_path)
        
        # Vytvoření long formátu - pouze sloupce, které skutečně existují v CSV
        all_question_codes = mapping_df['question_code'].tolist()
        question_cols = [col for col in all_question_codes if col in df.columns]
        id_cols = [col for col in df.columns if col not in question_cols]
        
        # Log informace o chybějících sloupcích
        missing_cols = [col for col in all_question_codes if col not in df.columns]
        if missing_cols:
            logger.info(f"Následující otázky z mapování nejsou v CSV (pravděpodobně hlavní otázky polí): {missing_cols}")
        
        # Převod na long formát
        long_df = pd.melt(df, 
                         id_vars=id_cols,
                         value_vars=question_cols,
                         var_name='question_code',
                         value_name='response')
        
        # Připojení názvů otázek
        long_df = long_df.merge(mapping_df[['question_code', 'question_name', 'question_text', 'is_main_question']],
                               left_on='question_code',
                               right_on='question_code',
                               how='left')
        
        # Úprava a export (už nemusíme dropovat 'code' sloupec)
        long_df['response'] = long_df['response'].fillna('')
        long_df.to_csv(output_path, index=False)
        
        logger.info(f"Data úspěšně transformována do long formátu: {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Chyba při transformaci do long formátu: {str(e)}")
        return False

def generate_chart_data(long_path: str, output_path: str) -> bool:
    """Generování datové struktury pro grafy podle LimeSurvey logiky"""
    if pd is None:
        logger.error("Pandas knihovna není dostupná. Pro generování dat pro grafy je potřeba nainstalovat pandas.")
        return False
        
    try:
        import os
        # Načtení long formátu
        df = pd.read_csv(long_path)
        
        # Agregace dat pro grafy
        chart_data = []
        
        # Identifikace hlavních otázek polí a jejich seskupení
        main_field_questions = {}  # {main_code: [subquestion_codes]}
        standalone_questions = []
        
        all_questions = df['question_code'].unique()
        
        for question in all_questions:
            # Detekce subotázek polí (obsahují [SQ...])
            if '[SQ' in question:
                # Extrakce hlavního kódu otázky (např. G6Q00001 z G6Q00001[SQ001])
                main_code = question.split('[')[0]
                if main_code not in main_field_questions:
                    main_field_questions[main_code] = []
                main_field_questions[main_code].append(question)
            else:
                standalone_questions.append(question)
        
        # Zpracování samostatných otázek (ne polí)
        for question in standalone_questions:
            question_df = df[df['question_code'] == question]
            
            # Detekce typu otázky na základě dat
            question_name = question_df['question_name'].iloc[0]
            unique_responses = question_df['response'].nunique()
            
            # Heuristika pro určení typu otázky
            question_type = 'single_choice'  # Výchozí
            if unique_responses > 10:
                question_type = 'scale'  # Škálové otázky
            elif any(resp for resp in question_df['response'].unique() if resp and len(str(resp)) > 50):
                question_type = 'text'  # Textové odpovědi
            
            # Základní informace o otázce
            question_info = {
                'code': question,
                'name': question_name,
                'type': question_type,
                'data': []
            }
            
            # Počty odpovědí
            response_counts = question_df['response'].value_counts()
            
            for response, count in response_counts.items():
                if response:  # Ignorování prázdných odpovědí
                    question_info['data'].append({
                        'label': response,
                        'value': int(count)
                    })
            
            chart_data.append(question_info)
        
        # Zpracování otázek polí - JEDEN graf pro celou hlavní otázku
        for main_code, subquestions in main_field_questions.items():
            # Získání názvu hlavní otázky z první subotázky
            first_subq_df = df[df['question_code'] == subquestions[0]]
            main_question_name = f"Otázka pole: {main_code}"
            
            # Pokus o získání lepšího názvu z mapování
            try:
                # Načtení question_mapping pro lepší název
                mapping_path = long_path.replace('responses_long.csv', 'question_mapping.csv')
                if os.path.exists(mapping_path):
                    mapping_df = pd.read_csv(mapping_path)
                    main_mapping = mapping_df[mapping_df['question_code'] == main_code]
                    if not main_mapping.empty:
                        main_question_name = main_mapping['question_name'].iloc[0]
            except:
                pass  # Použij výchozí název
            
            # Vytvoření kontingenční tabulky pro otázku pole
            field_data = []
            all_responses = set()
            
            # Sběr všech možných odpovědí
            for subq in subquestions:
                subq_df = df[df['question_code'] == subq]
                all_responses.update(subq_df['response'].dropna().unique())
            
            # Vytvoření dat pro každou subotázku
            for subq in subquestions:
                subq_df = df[df['question_code'] == subq]
                subq_name = subq_df['question_name'].iloc[0] if not subq_df.empty else subq
                
                # Počty odpovědí pro tuto subotázku
                response_counts = subq_df['response'].value_counts()
                
                subq_data = {
                    'subquestion': subq_name,
                    'responses': {}
                }
                
                for response in all_responses:
                    subq_data['responses'][response] = int(response_counts.get(response, 0))
                
                field_data.append(subq_data)
            
            # Základní informace o hlavní otázce pole
            question_info = {
                'code': main_code,
                'name': main_question_name,
                'type': 'array',  # Typ pole/matice
                'data': field_data,
                'subquestions': subquestions
            }
            
            chart_data.append(question_info)
        
        # Export do JSON
        import json
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(chart_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"Data pro grafy úspěšně vygenerována: {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Chyba při generování dat pro grafy: {str(e)}")
        return False

def validate_csv_structure(file_path: str) -> bool:
    """Validace struktury CSV souboru"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Validace CSV se středníkem jako oddělovačem
        reader = csv.reader(StringIO(content), delimiter=';', quotechar='"')
        headers = next(reader)
        row_count = 0
        invalid_rows = []
        expected_columns = len(headers)
        
        # Kontrola každého řádku a pole
        for i, row in enumerate(reader, 2):
            row_count += 1
            
            # Kontrola počtu sloupců
            if len(row) != expected_columns:
                invalid_rows.append(f"Řádek {i}: očekáváno {expected_columns} sloupců, nalezeno {len(row)}")
                continue
                
            # Kontrola textových polí
            for j, field in enumerate(row):
                # Kontrola neuzavřených uvozovek
                if field.count('"') % 2 != 0:
                    invalid_rows.append(f"Řádek {i}, sloupec {j+1}: neuzavřené uvozovky")
                    
                # Kontrola zalomení řádků
                if '\n' in field and not (field.startswith('"') and field.endswith('"')):
                    invalid_rows.append(f"Řádek {i}, sloupec {j+1}: nezabalené zalomení řádku")
                    
                # Kontrola escapovaných uvozovek
                if '""' not in field and '"' in field[1:-1]:
                    invalid_rows.append(f"Řádek {i}, sloupec {j+1}: neescapované uvozovky")
                
        if invalid_rows:
            logger.warning(f"Nalezeny chyby v CSV:\n" + "\n".join(invalid_rows))
            return False
                
        logger.info(f"Soubor {file_path} úspěšně validován ({row_count} řádků)")
        return True
        
    except Exception as e:
        logger.error(f"Chyba při validaci CSV: {str(e)}")
        return False
