"""
Enhanced Chart Generator
Roz<PERSON><PERSON><PERSON><PERSON>ý generátor graf<PERSON> pro různé typy LimeSurvey otázek
"""

try:
    import pandas as pd
except ImportError:
    pd = None

import json
import os
from typing import Dict, List, Optional, Any
from src.chart_generator import ChartGenerator
from src.lss_question_analyzer import LSSQuestionAnalyzer
from src.enhanced_data_transformer import EnhancedDataTransformer
from src.logger import get_logger

logger = get_logger(__name__)

class EnhancedChartGenerator(ChartGenerator):
    """Rozšířený generátor grafů s podporou všech typů LimeSurvey otázek"""
    
    def __init__(self, survey_title="", data_source="", data_source_link="", byline="",
                 png_width=600, png_border=10, png_scale=2, auto_height=True,
                 full_header_footer=False, transparent_bg=False):
        
        super().__init__(survey_title, data_source, data_source_link, byline,
                        png_width, png_border, png_scale, auto_height,
                        full_header_footer, transparent_bg)
        
        # <PERSON>ozšíř<PERSON><PERSON> mapování typů grafů
        self.chart_types.update({
            # Základní grafy
            'column-chart': 'd3-bars',
            'grouped-column-chart': 'd3-bars-grouped',
            'stacked-column-chart': 'd3-bars-stacked',
            'diverging-bar-chart': 'd3-bars-split',
            
            # Koláčové grafy (všechny jako donut)
            'pie-chart': 'd3-donuts',
            'donut-chart': 'd3-donuts',
            'multiple-pies': 'd3-multiple-donuts',
            
            # Čárové grafy
            'line-chart': 'd3-lines',
            'multiple-lines': 'd3-multiple-lines',
            'area-chart': 'd3-area',
            
            # Bodové grafy
            'scatter-plot': 'd3-scatter-plot',
            'dot-plot': 'd3-dot-plot',
            
            # Speciální grafy
            'histogram': 'd3-bars',
            'ranking-chart': 'd3-bars',
            'timeline': 'd3-lines',
            
            # Textové grafy (fallback na tabulku)
            'word-cloud': 'table',  # Datawrapper nemá word cloud, použijeme tabulku
            'table': 'table'
        })
        
        # Inicializace analyzeru a transformeru
        self.analyzer = LSSQuestionAnalyzer()
        self.transformer = EnhancedDataTransformer()
        
        self.logger = logger
    
    def generate_charts_from_prepared_data(self, chart_data_path: str, responses_long_path: str,
                                          question_mapping_path: str, output_base: str,
                                          survey_id: str, question_ids: List[str] = None) -> List[Dict]:
        """
        Generuje grafy z připravených dat (stejný workflow jako Menu 7)
        
        Args:
            chart_data_path: Cesta k chart_data.json
            responses_long_path: Cesta k responses_long.csv
            question_mapping_path: Cesta k question_mapping.csv
            output_base: Základní cesta pro výstup
            survey_id: ID průzkumu
            question_ids: Seznam ID otázek k zpracování (None = všechny)
            
        Returns:
            Seznam výsledků generování grafů
        """
        if pd is None:
            logger.error("Pandas knihovna není dostupná")
            return []
            
        if self.dw is None:
            logger.error("DatawrapperClient není dostupný")
            return []
        
        try:
            # Načtení připravených dat
            self.logger.info(f"Načítám připravená data z: {chart_data_path}")
            with open(chart_data_path, 'r', encoding='utf-8') as f:
                chart_data = json.load(f)
            
            self.logger.info(f"Načítám long formát dat z: {responses_long_path}")
            responses_long_df = pd.read_csv(responses_long_path)
            
            self.logger.info(f"Načítám mapování otázek z: {question_mapping_path}")
            question_mapping_df = pd.read_csv(question_mapping_path)
            
            # Filtrování podle zadaných ID
            if question_ids:
                chart_data = [chart for chart in chart_data
                             if chart['code'] in question_ids]
            
            self.logger.info(f"Generuji {len(chart_data)} grafů z připravených dat")
            
            # Vytvoření výstupní složky
            self.survey_id = survey_id
            output_dir = f"{output_base}/{survey_id}"
            os.makedirs(output_dir, exist_ok=True)
            
            # Vytvoření/získání složky na serveru
            folder = self.dw.create_folder(survey_id)
            if not folder:
                self.logger.warning("Nepodařilo se vytvořit složku na serveru")
            
            results = []
            
            # Generování jednotlivých grafů
            for i, chart_config in enumerate(chart_data):
                try:
                    self.logger.info(f"Generuji graf {i+1}/{len(chart_data)}: "
                                   f"{chart_config['name']}")
                    
                    result = self._generate_single_chart_from_prepared_data(
                        chart_config, responses_long_df, question_mapping_df, output_dir, folder
                    )
                    
                    if result:
                        results.append(result)
                        
                except Exception as e:
                    self.logger.error(f"Chyba při generování grafu {chart_config['code']}: {str(e)}")
                    results.append({
                        'question_id': chart_config['code'],
                        'status': 'error',
                        'error': str(e)
                    })
            
            self.logger.info(f"Dokončeno generování: {len(results)} grafů")
            return results
            
        except Exception as e:
            self.logger.error(f"Chyba při generování grafů z připravených dat: {str(e)}")
            return []

    def generate_charts_from_lss(self, lss_path: str, responses_path: str,
                                output_base: str, question_ids: List[str] = None) -> List[Dict]:
        """
        DEPRECATED: Generuje grafy na základě LSS struktury a odpovědí
        Použijte místo toho generate_charts_from_prepared_data()
        
        Args:
            lss_path: Cesta k LSS souboru
            responses_path: Cesta k CSV s odpověďmi
            output_base: Základní cesta pro výstup
            question_ids: Seznam ID otázek k zpracování (None = všechny)
            
        Returns:
            Seznam výsledků generování grafů
        """
        self.logger.warning("generate_charts_from_lss je deprecated. Použijte generate_charts_from_prepared_data()")
        
        if pd is None:
            logger.error("Pandas knihovna není dostupná")
            return []
            
        if self.dw is None:
            logger.error("DatawrapperClient není dostupný")
            return []
        
        try:
            # Analýza LSS struktury
            self.logger.info(f"Analyzuji LSS strukturu: {lss_path}")
            lss_analysis = self.analyzer.analyze_lss_structure(lss_path)
            
            if 'error' in lss_analysis:
                self.logger.error(f"Chyba při analýze LSS: {lss_analysis['error']}")
                return []
            
            # Načtení odpovědí
            self.logger.info(f"Načítám odpovědi: {responses_path}")
            responses_df = pd.read_csv(responses_path, sep=';')
            
            # Získání doporučených grafů
            recommended_charts = self.analyzer.get_recommended_charts(lss_analysis)
            
            # Filtrování podle zadaných ID
            if question_ids:
                recommended_charts = [chart for chart in recommended_charts
                                    if str(chart['question_id']) in question_ids]
            
            self.logger.info(f"Generuji {len(recommended_charts)} grafů")
            
            # Vytvoření výstupní složky
            survey_id = lss_analysis.get('survey_id', 'unknown')
            self.survey_id = survey_id  # Nastavíme survey_id pro použití v _filter_question_data
            output_dir = f"{output_base}/{survey_id}"
            os.makedirs(output_dir, exist_ok=True)
            
            # Vytvoření/získání složky na serveru
            folder = self.dw.create_folder(survey_id)
            if not folder:
                self.logger.warning("Nepodařilo se vytvořit složku na serveru")
            
            results = []
            
            # Generování jednotlivých grafů
            for i, chart_config in enumerate(recommended_charts):
                try:
                    self.logger.info(f"Generuji graf {i+1}/{len(recommended_charts)}: "
                                   f"{chart_config['question_title']}")
                    
                    result = self._generate_single_chart_from_lss(
                        chart_config, responses_df, output_dir, folder
                    )
                    
                    if result:
                        results.append(result)
                        
                except Exception as e:
                    self.logger.error(f"Chyba při generování grafu {chart_config['question_id']}: {str(e)}")
                    results.append({
                        'question_id': chart_config['question_id'],
                        'status': 'error',
                        'error': str(e)
                    })
            
            self.logger.info(f"Dokončeno generování: {len(results)} grafů")
            return results
            
        except Exception as e:
            self.logger.error(f"Chyba při generování grafů z LSS: {str(e)}")
            return []
    
    def _generate_single_chart_from_prepared_data(self, chart_config: Dict[str, Any],
                                                 responses_long_df, question_mapping_df,
                                                 output_dir: str, folder: Optional[Dict] = None) -> Optional[Dict]:
        """
        Generuje jeden graf z připravených dat (chart_data.json formát)
        
        Args:
            chart_config: Konfigurace grafu z chart_data.json
            responses_long_df: DataFrame s long formátem dat
            question_mapping_df: DataFrame s mapováním otázek
            output_dir: Výstupní složka
            folder: Složka na Datawrapper serveru
            
        Returns:
            Výsledek generování grafu
        """
        try:
            question_code = chart_config['code']
            question_name = chart_config['name']
            question_type = chart_config['type']
            chart_data = chart_config['data']
            
            # Najdeme mapování pro tuto otázku
            question_mapping = question_mapping_df[
                question_mapping_df['question_code'] == question_code
            ]
            
            if question_mapping.empty:
                self.logger.warning(f"Nenalezeno mapování pro otázku {question_code}")
                question_text = question_name  # Fallback na název z chart_data
            else:
                question_text = question_mapping.iloc[0]['question_text']
            
            # Určení typu grafu podle typu otázky
            datawrapper_type = self.chart_types.get(question_type, 'd3-bars')
            
            # Příprava dat pro Datawrapper
            if not chart_data:
                self.logger.warning(f"Žádná data k zobrazení pro otázku {question_code}")
                return None
            
            # Konverze dat do formátu pro Datawrapper
            dw_data = []
            
            if question_type == 'array':
                # Otázky polí (array) - kontingenční tabulka
                # Struktura: {'subquestion': '...', 'responses': {'odpověď1': počet, 'odpověď2': počet}}

                # Získání všech možných odpovědí a jejich seřazení podle hodnoty
                all_responses = set()
                for item in chart_data:
                    if 'responses' in item:
                        all_responses.update(item['responses'].keys())

                # Seřazení odpovědí podle číselné hodnoty (pro škály) nebo abecedně
                def sort_response_key(response):
                    try:
                        # Pokusíme se převést na číslo pro správné řazení škál
                        return float(response)
                    except (ValueError, TypeError):
                        # Pokud není číslo, seřadíme abecedně
                        return str(response)

                response_columns = sorted(list(all_responses), key=sort_response_key)

                # Vytvoření kontingenční tabulky
                # Hlavička: Subotázka, Odpověď1, Odpověď2, Odpověď3, ...
                for item in chart_data:
                    subquestion = item['subquestion']
                    responses = item['responses']

                    # Vytvoř řádek kontingenční tabulky
                    row = {'Subotázka': subquestion}
                    for response_col in response_columns:
                        row[str(response_col)] = responses.get(response_col, 0)

                    dw_data.append(row)

                self.logger.info(f"Array data připravena jako kontingenční tabulka: "
                               f"{len(dw_data)} řádků × {len(response_columns)} sloupců")
            else:
                # Samostatné otázky - standardní formát
                # Struktura: {'label': '...', 'value': ...}
                for item in chart_data:
                    dw_data.append({
                        'label': item['label'],
                        'value': item['value']
                    })
            
            # Vytvoření grafu na Datawrapper s metadaty
            chart_title = question_text or question_name
            chart_description = self.survey_title
            
            chart = self.dw.create_chart(
                title=chart_title,
                chart_type=datawrapper_type,
                description=chart_description,
                data_source=self.data_source,
                data_source_link=self.data_source_link,
                byline=self.byline,
                folder_id=folder['id'] if folder else None
            )
            
            if not chart:
                self.logger.error(f"Nepodařilo se vytvořit graf pro otázku {question_code}")
                return None
            
            # Nahrání dat
            if not self.dw.update_chart_data(chart['id'], dw_data):
                self.logger.error(f"Nepodařilo se nahrát data pro graf {chart['id']}")
                return None

            # Nastavení metadat pro lepší vizualizaci
            chart_metadata = self._prepare_chart_metadata_for_array(
                question_type, datawrapper_type, dw_data
            )

            if chart_metadata and not self.dw.update_chart_metadata(chart['id'], chart_metadata):
                self.logger.warning(f"Nepodařilo se aktualizovat metadata grafu {chart['id']}")

            # Publikování grafu
            if not self.dw.publish_chart(chart['id']):
                self.logger.error(f"Nepodařilo se publikovat graf {chart['id']}")
                return None
            
            # Export PNG
            png_filename = f"{question_code}_{question_name.replace('/', '_').replace('?', '')}.png"
            png_path = os.path.join(output_dir, png_filename)
            
            if self.dw.export_chart(
                chart['id'],
                png_path,
                width=self.png_width,
                border=self.png_border,
                scale=self.png_scale,
                auto_height=self.auto_height,
                full_header_footer=self.full_header_footer,
                transparent_bg=self.transparent_bg
            ):
                self.logger.info(f"Graf exportován: {png_path}")
                
                return {
                    'question_id': question_code,
                    'question_title': question_name,
                    'chart_type': datawrapper_type,
                    'chart_id': chart['id'],
                    'chart_url': chart.get('url', ''),
                    'png_path': png_path,
                    'status': 'success'
                }
            else:
                self.logger.error(f"Nepodařilo se exportovat graf {chart['id']}")
                return None
                
        except Exception as e:
            self.logger.error(f"Chyba při generování grafu z připravených dat: {str(e)}")
            return None

    def _generate_single_chart_from_lss(self, chart_config: Dict[str, Any],
                                       responses_df, output_dir: str,
                                       folder: Optional[Dict] = None) -> Optional[Dict]:
        """
        Generuje jeden graf na základě konfigurace z LSS analýzy
        
        Args:
            chart_config: Konfigurace grafu z LSS analýzy
            responses_df: DataFrame s odpověďmi
            output_dir: Výstupní složka
            folder: Složka na Datawrapper serveru
            
        Returns:
            Výsledek generování grafu
        """
        try:
            question_id = chart_config['question_id']
            question_title = chart_config.get('question_text', chart_config.get('question_title', ''))
            chart_type = chart_config['chart_type']
            datawrapper_type = chart_config['datawrapper_type']
            
            # Filtrování dat pro konkrétní otázku
            question_data = self._filter_question_data(responses_df, question_id, question_title)
            
            if question_data.empty:
                self.logger.warning(f"Žádná data pro otázku {question_id}")
                return None
            
            # Příprava dat podle typu otázky
            chart_data = self.transformer.prepare_chart_data_by_analysis(
                question_data, chart_config
            )
            
            if not chart_data.get('data'):
                self.logger.warning(f"Žádná data k zobrazení pro otázku {question_id}")
                return None
            
            # Vytvoření grafu na Datawrapper s metadaty
            # Použijeme skutečný název otázky jako nadpis
            chart_title = question_title or f"Otázka {question_id}"
            chart_description = chart_config.get('question_text', '')[:200]
            
            chart = self.dw.create_chart(
                title=chart_title,
                chart_type=datawrapper_type,
                folder_id=folder.get('id') if folder else None,
                description=self.survey_title,
                data_source=self.data_source or "LimeSurvey",
                data_source_link=self.data_source_link or "",
                byline=self.byline or f"Typ otázky: {chart_data.get('data_type', 'categorical')}"
            )
            
            if not chart:
                self.logger.error(f"Nepodařilo se vytvořit graf pro otázku {question_id}")
                return None
            
            # Příprava dat pro Datawrapper
            dw_data = self._prepare_datawrapper_data(chart_data, chart_type)
            
            # Nahrání dat
            if not self.dw.update_chart_data(chart['id'], dw_data):
                self.logger.error(f"Nepodařilo se nahrát data pro graf {chart['id']}")
                return None
            
            # Publikování grafu
            if not self.dw.publish_chart(chart['id']):
                self.logger.error(f"Nepodařilo se publikovat graf {chart['id']}")
                return None
            
            # Export PNG
            png_data = self.dw.export_chart(
                chart_id=chart['id'],
                export_format='png',
                target_width=self.png_width,
                border_width=self.png_border,
                zoom=self.png_scale,
                plain=not self.full_header_footer
            )
            
            if png_data:
                # Uložení PNG
                png_filename = f"{question_id}_{question_title.replace(' ', '_')[:50]}.png"
                png_path = os.path.join(output_dir, png_filename)
                
                with open(png_path, 'wb') as f:
                    f.write(png_data)
                
                self.logger.info(f"Graf uložen: {png_path}")
                
                return {
                    'question_id': question_id,
                    'question_title': question_title,
                    'chart_id': chart['id'],
                    'chart_type': chart_type,
                    'datawrapper_type': datawrapper_type,
                    'png_path': png_path,
                    'chart_url': chart.get('publicUrl', ''),
                    'data_points': len(chart_data.get('data', [])),
                    'status': 'success'
                }
            else:
                self.logger.error(f"Nepodařilo se exportovat PNG pro graf {chart['id']}")
                return {
                    'question_id': question_id,
                    'chart_id': chart['id'],
                    'status': 'export_failed'
                }
                
        except Exception as e:
            self.logger.error(f"Chyba při generování grafu: {str(e)}")
            return {
                'question_id': chart_config.get('question_id', 'unknown'),
                'status': 'error',
                'error': str(e)
            }
    
    def _filter_question_data(self, responses_df, question_id: str, question_title: str):
        """Filtruje data pro konkrétní otázku z long formátu"""
        try:
            # Pokusíme se načíst long formát dat s mapovanými labely
            long_path = f"data/{self.survey_id}/responses_long.csv"
            mapping_path = f"data/{self.survey_id}/question_mapping.csv"
            
            if os.path.exists(long_path) and os.path.exists(mapping_path):
                long_df = pd.read_csv(long_path)
                mapping_df = pd.read_csv(mapping_path)
                
                # Najdeme question_code pro dané question_id
                # Nejprve zkusíme najít podle question_title (který obsahuje kód jako "G2Q00002")
                question_code = None
                
                # Pokud question_title vypadá jako kód (např. "G2Q00002"), použijeme ho přímo
                if question_title and question_title.startswith('G') and 'Q' in question_title:
                    if question_title in mapping_df['code'].values:
                        question_code = question_title
                        self.logger.info(f"Nalezen question_code podle title: {question_code}")
                
                # Fallback - hledáme podle různých možných formátů
                if not question_code:
                    possible_codes = [
                        f"G{question_id}Q00001",  # Standardní formát
                        f"G{question_id}",        # Zkrácený formát
                        question_id,              # Přímé ID
                        f"Q{question_id}"         # Q prefix
                    ]
                    
                    for code in possible_codes:
                        if code in mapping_df['code'].values:
                            question_code = code
                            break
                    
                    # Pokud nenajdeme přesnou shodu, zkusíme hledat částečnou
                    if not question_code:
                        for _, row in mapping_df.iterrows():
                            if str(question_id) in str(row['code']) or str(row['code']).endswith(f"Q{question_id:05d}"):
                                question_code = row['code']
                                break
                
                if question_code:
                    # Filtrování dat pro konkrétní otázku podle question_code
                    question_data = long_df[long_df['question_code'] == question_code].copy()
                    
                    if not question_data.empty:
                        # Vytvoření DataFrame s response hodnotami (už obsahují správné labely)
                        filtered_df = pd.DataFrame({
                            'response': question_data['response'].fillna('')
                        })
                        
                        # Odstranění prázdných řádků
                        filtered_df = filtered_df.dropna(how='all')
                        filtered_df = filtered_df[filtered_df['response'] != '']
                        
                        self.logger.info(f"Použita long formát data pro otázku {question_id} (kód: {question_code}) s {len(filtered_df)} odpověďmi")
                        return filtered_df
                    else:
                        self.logger.warning(f"Nenalezena data pro otázku {question_id} (kód: {question_code}) v long formátu")
                else:
                    self.logger.warning(f"Nenalezen question_code pro otázku {question_id} v mapování")
            
            # Fallback na původní metodu s raw CSV daty
            self.logger.warning(f"Long formát nebo mapování nedostupné, používám raw CSV data pro otázku {question_id}")
            
            # Hledání sloupců souvisejících s otázkou
            possible_columns = [
                question_title,
                f"Q{question_id}",
                f"question_{question_id}",
                str(question_id)
            ]
            
            # Hledání sloupců obsahujících ID nebo title otázky
            matching_columns = []
            for col in responses_df.columns:
                for possible in possible_columns:
                    if str(possible) in str(col) or str(col) in str(possible):
                        matching_columns.append(col)
                        break
            
            if not matching_columns:
                # Fallback - použijeme první sloupec s daty
                self.logger.warning(f"Nenalezeny sloupce pro otázku {question_id}, používám první dostupný")
                if len(responses_df.columns) > 0:
                    matching_columns = [responses_df.columns[0]]
            
            if matching_columns:
                # Vytvoření DataFrame s relevantními sloupci
                filtered_df = responses_df[matching_columns].copy()
                
                # Přejmenování hlavního sloupce na 'response' pro konzistenci
                if len(matching_columns) == 1:
                    filtered_df = filtered_df.rename(columns={matching_columns[0]: 'response'})
                
                # Odstranění prázdných řádků
                filtered_df = filtered_df.dropna(how='all')
                
                return filtered_df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"Chyba při filtrování dat pro otázku {question_id}: {str(e)}")
            return pd.DataFrame()
    
    def _prepare_datawrapper_data(self, chart_data: Dict[str, Any], chart_type: str):
        """Připraví data ve formátu pro Datawrapper"""
        try:
            data_type = chart_data.get('data_type', 'categorical')
            
            if data_type in ['categorical', 'binary', 'scale']:
                return self._prepare_simple_data(chart_data)
            elif data_type == 'multiple_choice':
                return self._prepare_multiple_choice_data_for_dw(chart_data)
            elif data_type in ['array', 'array_scale', 'array_binary', 'array_choice']:
                return self._prepare_array_data_for_dw(chart_data)
            elif data_type == 'numerical':
                return self._prepare_numerical_data_for_dw(chart_data)
            elif data_type == 'ranking':
                return self._prepare_ranking_data_for_dw(chart_data)
            elif data_type in ['text', 'long_text']:
                return self._prepare_text_data_for_dw(chart_data)
            else:
                return self._prepare_simple_data(chart_data)
                
        except Exception as e:
            self.logger.error(f"Chyba při přípravě dat pro Datawrapper: {str(e)}")
            return self._prepare_fallback_data_for_dw(chart_data)
    
    def _prepare_simple_data(self, chart_data: Dict[str, Any]):
        """Připraví jednoduchá kategorická data"""
        df_data = []
        for item in chart_data.get('data', []):
            df_data.append({
                'Kategorie': item.get('label', ''),
                'Počet': item.get('value', 0)
            })
        
        return pd.DataFrame(df_data)
    
    def _prepare_multiple_choice_data_for_dw(self, chart_data: Dict[str, Any]):
        """Připraví data pro multiple choice grafy"""
        # Pro multiple choice vytvoříme pivot tabulku
        df_data = []
        categories = set()
        labels = set()
        
        for item in chart_data.get('data', []):
            categories.add(item.get('category', ''))
            labels.add(item.get('label', ''))
        
        # Vytvoření pivot struktury
        for category in categories:
            row = {'Kategorie': category}
            for label in labels:
                # Najdeme hodnotu pro tuto kombinaci
                value = 0
                for item in chart_data.get('data', []):
                    if item.get('category') == category and item.get('label') == label:
                        value = item.get('value', 0)
                        break
                row[label] = value
            df_data.append(row)
        
        return pd.DataFrame(df_data)
    
    def _prepare_array_data_for_dw(self, chart_data: Dict[str, Any]):
        """Připraví data pro array grafy"""
        # Podobně jako multiple choice, ale s důrazem na škály
        categories = chart_data.get('categories', [])
        scale_labels = chart_data.get('scale_labels', [])
        
        if not scale_labels:
            # Extrahujeme labels z dat
            scale_labels = list(set(item.get('label', '') for item in chart_data.get('data', [])))
            scale_labels.sort()
        
        df_data = []
        for category in categories:
            row = {'Podotázka': category}
            for label in scale_labels:
                # Najdeme hodnotu pro tuto kombinaci
                value = 0
                for item in chart_data.get('data', []):
                    if item.get('category') == category and item.get('label') == label:
                        value = item.get('value', 0)
                        break
                row[label] = value
            df_data.append(row)
        
        return pd.DataFrame(df_data)
    
    def _prepare_numerical_data_for_dw(self, chart_data: Dict[str, Any]):
        """Připraví číselná data pro histogram"""
        df_data = []
        for item in chart_data.get('data', []):
            df_data.append({
                'Interval': item.get('label', ''),
                'Četnost': item.get('value', 0)
            })
        
        return pd.DataFrame(df_data)
    
    def _prepare_ranking_data_for_dw(self, chart_data: Dict[str, Any]):
        """Připraví ranking data"""
        df_data = []
        for item in chart_data.get('data', []):
            df_data.append({
                'Možnost': item.get('label', ''),
                'Průměrné pořadí': item.get('average_rank', 0),
                'Počet hodnocení': item.get('value', 0)
            })
        
        return pd.DataFrame(df_data)
    
    def _prepare_text_data_for_dw(self, chart_data: Dict[str, Any]):
        """Připraví textová data (word frequency)"""
        df_data = []
        for item in chart_data.get('data', []):
            df_data.append({
                'Slovo': item.get('label', ''),
                'Četnost': item.get('value', 0)
            })
        
        return pd.DataFrame(df_data)
    
    def _prepare_fallback_data_for_dw(self, chart_data: Dict[str, Any]):
        """Fallback příprava dat"""
        return pd.DataFrame([{'Kategorie': 'Bez dat', 'Hodnota': 0}])
    
    def get_supported_question_types(self) -> Dict[str, str]:
        """Vrátí seznam podporovaných typů otázek"""
        return {
            limesurvey_type: mapping['description'] 
            for limesurvey_type, mapping in self.analyzer.QUESTION_TYPE_MAPPING.items()
            if mapping.get('chart_type') is not None
        }
    
    def analyze_lss_file(self, lss_path: str) -> Dict[str, Any]:
        """Analyzuje LSS soubor a vrátí přehled"""
        return self.analyzer.analyze_lss_structure(lss_path)
    
    def _prepare_chart_metadata(self, chart_config: Dict[str, Any], chart_data: Dict[str, Any], datawrapper_type: str) -> Dict[str, Any]:
        """Připraví metadata pro graf (nadpis, popisky os, atd.)"""
        try:
            question_title = chart_config.get('question_title', '')
            question_text = chart_config.get('question_text', '')
            data_type = chart_data.get('data_type', 'categorical')
            
            # Základní metadata
            metadata = {
                'describe': {
                    'intro': question_text[:200] if question_text else '',
                    'byline': f'Typ otázky: {data_type}',
                    'source-name': 'LimeSurvey',
                    'source-url': ''
                },
                'visualize': {
                    'highlighted-series': [],
                    'highlighted-values': []
                }
            }
            
            # Specifická nastavení podle typu grafu
            if datawrapper_type in ['column-chart', 'grouped-column-chart', 'stacked-column-chart', 'd3-bars', 'd3-bars-grouped', 'd3-bars-stacked']:
                metadata['visualize'].update({
                    'base-color': 1,
                    'custom-colors': {},
                    'labeling': {
                        'show': True,
                        'position': 'outside'
                    }
                })
                
                # Popisky os
                if 'axes' not in metadata:
                    metadata['axes'] = {}
                
                metadata['axes'].update({
                    'x': {
                        'title': {
                            'text': 'Kategorie'
                        }
                    },
                    'y': {
                        'title': {
                            'text': 'Počet odpovědí'
                        }
                    }
                })
                
            elif datawrapper_type in ['pie-chart', 'd3-pies', 'd3-donuts']:
                metadata['visualize'].update({
                    'base-color': 1,
                    'custom-colors': {},
                    'labeling': {
                        'show': True,
                        'position': 'outside'
                    }
                })
                
            elif datawrapper_type == 'table':
                metadata['visualize'].update({
                    'compact': False,
                    'stripe-rows': True
                })
            
            return metadata

        except Exception as e:
            self.logger.error(f"Chyba při přípravě metadat: {str(e)}")
            return {}

    def _prepare_chart_metadata_for_array(self, question_type: str, datawrapper_type: str, dw_data: list) -> Dict[str, Any]:
        """Připraví metadata specificky pro array grafy s řazením hodnot"""
        try:
            if question_type != 'array' or not dw_data:
                return {}

            metadata = {}

            # Pro stack sloupcové grafy nastavíme řazení podle celkových hodnot
            if datawrapper_type in ['d3-bars-stacked', 'stacked-column-chart']:
                # Vypočítáme celkové hodnoty pro každou subotázku
                if dw_data and isinstance(dw_data[0], dict):
                    # Získáme názvy sloupců s hodnotami (vše kromě 'Subotázka')
                    value_columns = [col for col in dw_data[0].keys() if col != 'Subotázka']

                    # Vypočítáme celkové hodnoty pro řazení
                    row_totals = []
                    for row in dw_data:
                        total = sum(row.get(col, 0) for col in value_columns if isinstance(row.get(col), (int, float)))
                        row_totals.append((row.get('Subotázka', ''), total))

                    # Seřadíme podle celkových hodnot (sestupně)
                    sorted_rows = sorted(row_totals, key=lambda x: x[1], reverse=True)
                    sorted_categories = [row[0] for row in sorted_rows]

                    metadata = {
                        'visualize': {
                            'sort-values': 'custom',
                            'custom-sort-order': sorted_categories,
                            'labeling': {
                                'show': True,
                                'position': 'inside'
                            },
                            'base-color': 1
                        },
                        'axes': {
                            'x': {
                                'title': {
                                    'text': 'Subotázky'
                                }
                            },
                            'y': {
                                'title': {
                                    'text': 'Počet odpovědí'
                                }
                            }
                        }
                    }

                    self.logger.info(f"Nastaveno řazení pro {len(sorted_categories)} subotázek podle celkových hodnot")

            return metadata

        except Exception as e:
            self.logger.error(f"Chyba při přípravě metadat pro array graf: {str(e)}")
            return {}