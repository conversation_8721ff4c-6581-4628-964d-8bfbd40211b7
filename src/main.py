
current_survey_id = None

def display_menu():
    global current_survey_id
    print("=" * 60)
    print("=== LIMWRAPP - Hlavní menu ===")
    if current_survey_id:
        print(f"Aktuální průzkum: {current_survey_id}")
    else:
        print("Aktuální průzkum: žádný")
    print("=" * 60)
    print("1. Vypsání seznamu průzkumů")
    print("2. Načtení dat průzkumu (CSV + LSS)")
    print("3. Načtení struktury průzkumu (LSS)")
    print("4. Propojení CSV a JSON souborů (mapování)")
    print("5. Transformace CSV dat na long formát")
    print("6. Generování datové struktury pro grafy")
    print("7. <PERSON>rov<PERSON><PERSON> grafů (původní)")
    print("8. 🆕 <PERSON>rov<PERSON>í grafů (rozšířené - všechny typy otázek)")
    print("9. 📥 Stažení PNG grafů")
    print("99. Zobrazení logu průzkumu")
    print("0. Ukončit program")
    print("=" * 60)

def list_surveys():
    from limesurvey_client import LimeSurveyClient
    """Získání seznamu průzkumů"""
    client = LimeSurveyClient()
    surveys = client.list_surveys()
    if not surveys:
        print("Nenalezeny žádné průzkumy")
        return None
        
    print("\nDostupné průzkumy:")
    for i, survey in enumerate(surveys, 1):
        print(f"{i}. {survey['surveyls_title']} (ID: {survey['sid']})")
        
    try:
        choice = input("\nVyberte číslo průzkumu (nebo Enter pro návrat): ")
    except EOFError:
        return None
    if not choice:
        return None
        
    try:
        survey_id = str(surveys[int(choice)-1]['sid'])
        print(f"Vybrané ID průzkumu: {survey_id}")
        return survey_id
    except (ValueError, IndexError):
        print("Neplatná volba")
        return None

def get_survey_id():
    """Získání ID průzkumu buď z výběru nebo od uživatele"""
    survey_id = list_surveys()
    if not survey_id:
        survey_id = input("Zadejte ID průzkumu: ")
    return survey_id

def load_survey_data():
    """Načtení dat průzkumu"""
    from limesurvey_client import LimeSurveyClient
    from data_transformer import decompress_csv, validate_csv_structure
    global current_survey_id
    survey_id = get_survey_id()
    if not survey_id:
        return
        
    print(f"Načítám data průzkumu {survey_id}...")
    print("=" * 50)
        
    try:
        client = LimeSurveyClient()
        
        # Stažení odpovědí
        print("1/4 Stahuji odpovědi z LimeSurvey API...")
        file_path = client.get_responses(survey_id)
        print(f"✓ Odpovědi staženy do: {file_path}")
        
        # Stažení struktury
        print("2/4 Stahuji strukturu průzkumu...")
        structure_path = client.get_survey_structure(survey_id)
        print(f"✓ Struktura uložena do: {structure_path}")
        
        # Dekomprese
        print("3/4 Dekomprimuji CSV data...")
        decompress_csv(file_path)
        print("✓ Data dekomprimována")
        
        # Validace
        print("4/4 Validuji strukturu CSV...")
        if validate_csv_structure(file_path):
            print("✓ CSV struktura je v pořádku")
        else:
            print("⚠ CSV obsahuje varování - zkontrolujte log pro detaily")
        
        current_survey_id = survey_id
        print("=" * 50)
        print(f"✓ Průzkum {survey_id} byl úspěšně načten a je připraven k dalšímu zpracování")
            
    except Exception as e:
        print("=" * 50)
        print(f"✗ Chyba při zpracování dat: {str(e)}")

def load_survey_structure():
    """Načtení struktury průzkumu"""
    from limesurvey_client import LimeSurveyClient
    survey_id = get_survey_id()
    if not survey_id:
        return
        
    print(f"Načítám strukturu průzkumu {survey_id}...")
    print("=" * 50)
        
    try:
        client = LimeSurveyClient()
        lss_path = client.get_survey_structure(survey_id)
        print(f"✓ Struktura načtena a uložena do: {lss_path}")
        
        # Načtení a zobrazení struktury ze souboru
        import json
        with open(lss_path, 'r', encoding='utf-8') as f:
            structure = json.load(f)
        
        print(f"\nPřehled struktury průzkumu {survey_id}:")
        print(f"Počet skupin: {len(structure['groups'])}")
        
        total_questions = sum(len(group['questions']) for group in structure['groups'])
        print(f"Celkem otázek: {total_questions}")
        
        print("\nSkupiny otázek:")
        for i, group in enumerate(structure['groups'], 1):
            print(f"{i}. {group['group_name']} ({len(group['questions'])} otázek)")
            for j, question in enumerate(group['questions'][:3], 1):  # Zobraz jen první 3
                print(f"   {j}. {question['question'][:80]}{'...' if len(question['question']) > 80 else ''}")
            if len(group['questions']) > 3:
                print(f"   ... a dalších {len(group['questions']) - 3} otázek")
        
        print("=" * 50)
        print("✓ Struktura průzkumu byla úspěšně načtena")
        
    except Exception as e:
        print("=" * 50)
        print(f"✗ Chyba při načítání struktury: {str(e)}")

def merge_csv_json():
    """Propojení CSV a JSON souborů"""
    from data_transformer import generate_question_mapping
    import os
    
    global current_survey_id
    if not current_survey_id:
        print("Není vybrán žádný průzkum. Vyberte průzkum:")
        current_survey_id = get_survey_id()
        if not current_survey_id:
            return

    print(f"Vytvářím mapování otázek pro průzkum {current_survey_id}...")
    print("=" * 50)

    try:
        csv_path = f"data/{current_survey_id}/responses.csv"
        lss_path = f"data/{current_survey_id}/structure.lss"
        output_path = f"data/{current_survey_id}/question_mapping.csv"
        
        # Kontrola existence potřebných souborů
        if not os.path.exists(csv_path):
            print(f"✗ CSV soubor neexistuje: {csv_path}")
            print("Nejprve načtěte data průzkumu (volba 2)")
            return
            
        if not os.path.exists(lss_path):
            print(f"✗ LSS soubor neexistuje: {lss_path}")
            print("Nejprve načtěte strukturu průzkumu (volba 2 nebo 3)")
            return
        
        print(f"Načítám CSV data z: {csv_path}")
        print(f"Načítám strukturu z: {lss_path}")
        print("Generuji mapování otázek...")
        
        if generate_question_mapping(csv_path, lss_path, output_path):
            print("=" * 50)
            print(f"✓ Mapování otázek bylo úspěšně vygenerováno do: {output_path}")
        else:
            print("=" * 50)
            print("✗ Chyba při generování mapování otázek - zkontrolujte log pro detaily")
    except Exception as e:
        print("=" * 50)
        print(f"✗ Chyba při propojování souborů: {str(e)}")

def transform_to_long():
    """Transformace CSV dat na long formát"""
    from data_transformer import transform_to_long_format
    import os
    
    global current_survey_id
    if not current_survey_id:
        print("Není vybrán žádný průzkum. Vyberte průzkum:")
        current_survey_id = get_survey_id()
        if not current_survey_id:
            return

    print(f"Transformuji data průzkumu {current_survey_id} do long formátu...")
    
    try:
        csv_path = f"data/{current_survey_id}/responses.csv"
        mapping_path = f"data/{current_survey_id}/question_mapping.csv"
        output_path = f"data/{current_survey_id}/responses_long.csv"
        
        # Kontrola existence potřebných souborů
        if not os.path.exists(csv_path):
            print(f"Chyba: CSV soubor neexistuje: {csv_path}")
            print("Nejprve načtěte data průzkumu (volba 2)")
            return
            
        if not os.path.exists(mapping_path):
            print(f"Chyba: Mapovací soubor neexistuje: {mapping_path}")
            print("Nejprve vytvořte mapování otázek (volba 4)")
            return
        
        print(f"Načítám data z: {csv_path}")
        print(f"Používám mapování z: {mapping_path}")
        
        if transform_to_long_format(csv_path, mapping_path, output_path):
            print(f"✓ Data byla úspěšně transformována do long formátu: {output_path}")
        else:
            print("✗ Chyba při transformaci dat")
            print("Poznámka: Pro transformaci dat je potřeba nainstalovat pandas knihovnu:")
            print("  sudo apt install python3-pandas")
            print("  nebo: pip install pandas")
    except Exception as e:
        print(f"✗ Chyba při transformaci dat: {str(e)}")

def generate_chart_structure():
    """Generování datové struktury pro grafy"""
    from data_transformer import generate_chart_data
    import os
    
    global current_survey_id
    if not current_survey_id:
        print("Není vybrán žádný průzkum. Vyberte průzkum:")
        current_survey_id = get_survey_id()
        if not current_survey_id:
            return

    print(f"Generuji datovou strukturu pro grafy průzkumu {current_survey_id}...")
    print("=" * 50)

    try:
        long_path = f"data/{current_survey_id}/responses_long.csv"
        output_path = f"data/{current_survey_id}/chart_data.json"
        
        # Kontrola existence long formátu
        if not os.path.exists(long_path):
            print(f"✗ Long formát neexistuje: {long_path}")
            print("Nejprve transformujte data do long formátu (volba 5)")
            return
        
        print(f"Načítám long formát z: {long_path}")
        print("Agreguji data pro grafy...")
        
        if generate_chart_data(long_path, output_path):
            print("=" * 50)
            print(f"✓ Data pro grafy byla úspěšně vygenerována: {output_path}")
        else:
            print("=" * 50)
            print("✗ Chyba při generování dat pro grafy")
            print("Poznámka: Pro generování dat je potřeba nainstalovat pandas knihovnu:")
            print("  sudo apt install python3-pandas")
            print("  nebo: pip install pandas")
    except Exception as e:
        print("=" * 50)
        print(f"✗ Chyba při generování dat pro grafy: {str(e)}")

def generate_charts_menu():
    """Generování grafů"""
    from chart_generator import ChartGenerator
    import os
    
    global current_survey_id
    if not current_survey_id:
        print("Není vybrán žádný průzkum. Vyberte průzkum:")
        current_survey_id = get_survey_id()
        if not current_survey_id:
            return

    print(f"Generuji grafy pro průzkum {current_survey_id}...")
    print("=" * 50)

    try:
        data_path = f"data/{current_survey_id}/chart_data.json"
        output_dir = f"charts/{current_survey_id}/"
        
        # Kontrola existence dat pro grafy
        if not os.path.exists(data_path):
            print(f"✗ Data pro grafy neexistují: {data_path}")
            print("Nejprve vygenerujte datovou strukturu pro grafy (volba 6)")
            return
        
        # Sběr metadat pro grafy
        print("Zadejte metadata pro grafy (můžete nechat prázdné):")
        try:
            survey_title = input("Název průzkumu (Description): ") or f"Průzkum {current_survey_id}"
            data_source = input("Zdroj dat (Data source): ") or ""
            data_source_link = input("Odkaz na zdroj dat (Link to data source): ") or ""
            byline = input("Autor/Byline: ") or ""
            
            print("\nNastavení PNG exportu:")
            print("ℹ️  Výška se vypočítá podle poměru stran publikovaného grafu")
            png_width = int(input("Cílová šířka PNG (px) [600]: ") or "600")
            png_border = int(input("Okraj/Border (px) [10]: ") or "10")
            png_scale = int(input("Zoom factor (násobič) [2]: ") or "2")
            full_header_input = input("Plná hlavička a patička? (y/n) [n]: ") or "n"
            full_header_footer = full_header_input.lower() in ['y', 'yes', 'ano']
            transparent_input = input("Transparentní pozadí? (y/n) [n]: ") or "n"
            transparent_bg = transparent_input.lower() in ['y', 'yes', 'ano']
            
            # Automatická výška podle poměru stran
            auto_height = True  # Vždy automatická výška (vypočítaná)
            
        except (EOFError, ValueError):
            # Fallback pro piped input nebo chybné hodnoty
            survey_title = f"Průzkum {current_survey_id}"
            data_source = ""
            data_source_link = ""
            byline = ""
            png_width = 600  # Cílová šířka
            png_border = 10
            png_scale = 2
            auto_height = True  # Vždy automatická výška (vypočítaná)
            full_header_footer = False
            transparent_bg = False
        
        print(f"Načítám data z: {data_path}")
        print("Generuji grafy...")
        
        generator = ChartGenerator(
            survey_title=survey_title,
            data_source=data_source,
            data_source_link=data_source_link,
            byline=byline,
            png_width=png_width,
            png_border=png_border,
            png_scale=png_scale,
            auto_height=auto_height,
            full_header_footer=full_header_footer,
            transparent_bg=transparent_bg
        )
        results = generator.generate_charts(data_path, output_dir, current_survey_id)
        
        if results:
            print("=" * 50)
            print(f"✓ Grafy byly úspěšně vygenerovány do: {output_dir}")
            print(f"Počet vygenerovaných grafů: {len(results)}")
        else:
            print("=" * 50)
            print("✗ Nepodařilo se vygenerovat žádné grafy")
            print("Možné příčiny:")
            print("1. Chybí pandas knihovna:")
            print("   sudo apt install python3-pandas")
            print("   nebo: pip install pandas")
            print("2. Chybí DatawrapperClient modul")
            print("3. Chybí konfigurace pro Datawrapper API")
            
    except Exception as e:
        print("=" * 50)
def generate_enhanced_charts_menu():
    """Rozšířené generování grafů pro všechny typy LimeSurvey otázek"""
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
    except ImportError as e:
        print("=" * 50)
        print("✗ Chyba při importu rozšířených modulů:")
        print(f"  {str(e)}")
        print("Použijte původní generování grafů (volba 7)")
        return
    
    import os
    import json
    
    global current_survey_id
    if not current_survey_id:
        print("Není vybrán žádný průzkum. Vyberte průzkum:")
        current_survey_id = get_survey_id()
        if not current_survey_id:
            return

    print(f"🚀 Rozšířené generování grafů pro průzkum {current_survey_id}...")
    print("=" * 60)

    try:
        # Kontrola existence připravených dat (stejný workflow jako Menu 7)
        chart_data_path = f"data/{current_survey_id}/chart_data.json"
        responses_long_path = f"data/{current_survey_id}/responses_long.csv"
        question_mapping_path = f"data/{current_survey_id}/question_mapping.csv"
        
        if not os.path.exists(chart_data_path):
            print(f"✗ Data pro grafy neexistují: {chart_data_path}")
            print("Nejprve vygenerujte datovou strukturu pro grafy (volba 6)")
            return
            
        if not os.path.exists(responses_long_path):
            print(f"✗ Long formát dat neexistuje: {responses_long_path}")
            print("Nejprve transformujte data do long formátu (volba 5)")
            return
            
        if not os.path.exists(question_mapping_path):
            print(f"✗ Mapování otázek neexistuje: {question_mapping_path}")
            print("Nejprve vytvořte mapování otázek (volba 4)")
            return
        
        # Načtení a zobrazení informací o připravených datech
        print("✅ Kontroluji připravená data...")
        with open(chart_data_path, 'r', encoding='utf-8') as f:
            chart_data = json.load(f)
        
        print(f"📊 Nalezeno {len(chart_data)} připravených grafů")
        
        # Zobrazení prvních několika grafů
        print(f"\n📈 Připravené grafy:")
        for i, chart in enumerate(chart_data[:10]):  # Zobrazíme prvních 10
            print(f"   {i+1}. {chart['name']} ({chart['code']}): {chart['type']}")
        
        if len(chart_data) > 10:
            print(f"   ... a dalších {len(chart_data) - 10} grafů")
        
        # Potvrzení od uživatele
        try:
            confirm = input(f"\nChcete vygenerovat všech {len(chart_data)} grafů? (a/n): ")
        except EOFError:
            return
            
        if confirm.lower() not in ['a', 'ano', 'y', 'yes']:
            print("Generování zrušeno")
            return
        
        # Sběr metadat pro grafy
        print("\n📝 Zadejte metadata pro grafy (můžete nechat prázdné):")
        try:
            survey_title = input("Název průzkumu: ") or f"Průzkum {current_survey_id}"
            data_source = input("Zdroj dat: ") or "LimeSurvey"
            data_source_link = input("Odkaz na zdroj: ") or ""
            byline = input("Autor: ") or ""

            print("\nNastavení PNG exportu:")
            print("ℹ️  Výška se vypočítá podle poměru stran publikovaného grafu")
            png_width = int(input("Cílová šířka PNG (px) [600]: ") or "600")
            png_border = int(input("Okraj/Border (px) [10]: ") or "10")
            png_scale = int(input("Zoom factor (násobič) [2]: ") or "2")
            full_header_input = input("Plná hlavička a patička? (y/n) [n]: ") or "n"
            full_header_footer = full_header_input.lower() in ['y', 'yes', 'ano']
            transparent_input = input("Transparentní pozadí? (y/n) [n]: ") or "n"
            transparent_bg = transparent_input.lower() in ['y', 'yes', 'ano']

            # Automatická výška podle poměru stran
            auto_height = True  # Vždy automatická výška (vypočítaná)

        except (EOFError, ValueError):
            # Fallback pro piped input nebo chybné hodnoty
            survey_title = f"Průzkum {current_survey_id}"
            data_source = "LimeSurvey"
            data_source_link = ""
            byline = ""
            png_width = 600  # Cílová šířka
            png_border = 10
            png_scale = 2
            auto_height = True  # Vždy automatická výška (vypočítaná)
            full_header_footer = False
            transparent_bg = False
        
        # Vytvoření enhanced chart generatoru
        generator = EnhancedChartGenerator(
            survey_title=survey_title,
            data_source=data_source,
            data_source_link=data_source_link,
            byline=byline,
            png_width=png_width,
            png_border=png_border,
            png_scale=png_scale,
            auto_height=auto_height,
            full_header_footer=full_header_footer,
            transparent_bg=transparent_bg
        )
        
        # Generování grafů z připravených dat
        output_dir = f"charts/{current_survey_id}"
        
        print(f"\n🎨 Generuji grafy z připravených dat...")
        print("=" * 50)
        
        results = generator.generate_charts_from_prepared_data(
            chart_data_path=chart_data_path,
            responses_long_path=responses_long_path,
            question_mapping_path=question_mapping_path,
            output_base="charts",
            survey_id=current_survey_id,
            question_ids=None  # Všechny otázky
        )
        
        # Vyhodnocení výsledků
        if results:
            successful = [r for r in results if r.get('status') == 'success']
            failed = [r for r in results if r.get('status') != 'success']
            
            print("=" * 50)
            print(f"✅ DOKONČENO!")
            print(f"📊 Úspěšně vygenerováno: {len(successful)} grafů")
            print(f"❌ Selhalo: {len(failed)} grafů")
            print(f"📁 Výstupní složka: {output_dir}")
            
            if successful:
                print(f"\n📈 Úspěšné grafy:")
                for result in successful[:5]:  # Zobrazíme prvních 5
                    print(f"   ✅ {result['question_title']}: {result['chart_type']}")
                    print(f"      📄 {result['png_path']}")
                    if result.get('chart_url'):
                        print(f"      🌐 {result['chart_url']}")
                
                if len(successful) > 5:
                    print(f"   ... a dalších {len(successful) - 5} grafů")
            
            if failed:
                print(f"\n❌ Selhané grafy:")
                for result in failed[:3]:  # Zobrazíme první 3 chyby
                    print(f"   ❌ {result.get('question_id', 'unknown')}: {result.get('error', 'Neznámá chyba')}")
        else:
            print("=" * 50)
            print("❌ Nepodařilo se vygenerovat žádné grafy")
            print("Možné příčiny:")
            print("1. Chybí pandas knihovna: pip install pandas")
            print("2. Chybí konfigurace pro Datawrapper API")
            print("3. Problémy s daty v responses.csv")
            
    except Exception as e:
        print("=" * 50)
        print(f"✗ Chyba při rozšířeném generování grafů: {str(e)}")
        print("Zkuste použít původní generování grafů (volba 7)")
        print(f"✗ Chyba při generování grafů: {str(e)}")

def download_png_charts():
    """Stažení PNG grafů z Datawrapper"""
    try:
        from datawrapper_client import DatawrapperClient
        from config_loader import load_config
        import requests
        import json
        import re
        import os

        print("📥 Stažení PNG grafů z Datawrapper")
        print("=" * 50)

        # Zadání ID průzkumu
        survey_id = input("Zadejte ID průzkumu: ").strip()
        if not survey_id:
            print("❌ ID průzkumu je povinné")
            return

        # Načtení konfigurace pro root adresář
        config = load_config()
        charts_root = config.get('charts_root', 'charts')

        # Cesta k adresáři s grafy
        charts_dir = os.path.join(charts_root, survey_id)

        # Kontrola existence adresáře
        if not os.path.exists(charts_dir):
            os.makedirs(charts_dir, exist_ok=True)
            print(f"📁 Vytvořen adresář: {charts_dir}")

        # Sběr parametrů pro PNG export
        print("\n📝 Nastavení PNG exportu:")
        try:
            png_width = int(input("Cílová šířka PNG (px) [600]: ") or "600")
            png_border = int(input("Okraj/Border (px) [10]: ") or "10")
            png_scale = int(input("Zoom factor (násobič) [2]: ") or "2")
            full_header_input = input("Plná hlavička a patička? (y/n) [n]: ") or "n"
            full_header_footer = full_header_input.lower() in ['y', 'yes', 'ano']
            transparent_input = input("Transparentní pozadí? (y/n) [n]: ") or "n"
            transparent_bg = transparent_input.lower() in ['y', 'yes', 'ano']
        except (EOFError, ValueError):
            png_width = 600
            png_border = 10
            png_scale = 2
            full_header_footer = False
            transparent_bg = False

        # Načtení Datawrapper konfigurace
        team_id = config.get('DATAWRAPPER_TEAM_ID')
        parent_folder_id = config.get('DATAWRAPPER_LIMESURVEY_FOLDER_ID')

        if not team_id or not parent_folder_id:
            print("❌ Chybí konfigurace DATAWRAPPER_TEAM_ID nebo DATAWRAPPER_LIMESURVEY_FOLDER_ID v .env")
            return

        # Inicializace Datawrapper klienta
        dw = DatawrapperClient()

        # Debug - zobrazíme všechny dostupné složky
        print(f"\n🔍 Debug - načítání všech složek z API...")
        debug_data = dw.debug_all_folders()
        print(f"📋 Debug data:")
        import json
        debug_str = json.dumps(debug_data, indent=2, ensure_ascii=False)
        if len(debug_str) > 3000:
            print(debug_str[:3000] + "\n... (zkráceno)")
        else:
            print(debug_str)

        # Hledání složky pro průzkum
        print(f"\n🔍 Hledání složky pro průzkum {survey_id}...")
        print(f"   Team ID: {team_id}")
        print(f"   Nadřazená složka: {parent_folder_id}")

        # Nejprve zobrazíme všechny dostupné složky
        print(f"\n📋 Zobrazení všech složek v {parent_folder_id}:")
        all_contents = dw.get_folder_contents(parent_folder_id)

        if not all_contents:
            print("❌ Nepodařilo se načíst obsah nadřazené složky")
            return

        folders = [item for item in all_contents if item.get('type') == 'folder']
        charts = [item for item in all_contents if item.get('type') == 'chart']

        print(f"   📁 Nalezeno {len(folders)} složek:")
        for folder in folders:
            print(f"      - ID: {folder.get('id')}, Název: '{folder.get('title')}'")

        print(f"   📊 Nalezeno {len(charts)} grafů v nadřazené složce")

        # Najdeme podsložku s názvem survey_id
        survey_folder_id = dw.find_subfolder_by_name(parent_folder_id, survey_id)

        if not survey_folder_id:
            print(f"\n❌ Složka '{survey_id}' nenalezena v nadřazené složce {parent_folder_id}")
            print("💡 Dostupné složky jsou uvedeny výše. Zkontrolujte název.")

            # Pokusíme se najít podobný název
            similar_folders = []
            for folder in folders:
                folder_name = folder.get('title', '')
                if survey_id in folder_name or folder_name in survey_id:
                    similar_folders.append(folder)

            if similar_folders:
                print(f"\n🔍 Možné podobné složky:")
                for folder in similar_folders:
                    print(f"   - ID: {folder.get('id')}, Název: '{folder.get('title')}'")

                # Nabídneme použití první podobné složky
                if len(similar_folders) == 1:
                    use_similar = input(f"\nPoužít složku '{similar_folders[0].get('title')}'? (y/n): ").lower()
                    if use_similar in ['y', 'yes', 'ano']:
                        survey_folder_id = similar_folders[0].get('id')
                        print(f"✅ Použita složka: {survey_folder_id}")

            if not survey_folder_id:
                # Alternativní přístup - hledání grafů v team složce
                print(f"\n🔄 Zkouším alternativní přístup - hledání grafů v team složce...")

                # Najdeme team v debug datech
                team_charts = []
                if 'folders' in debug_data and 'data' in debug_data['folders']:
                    for item in debug_data['folders']['data']['list']:
                        if item.get('type') == 'team' and item.get('id') == team_id:
                            team_charts = item.get('charts', [])
                            break

                if not team_charts:
                    print("❌ Nepodařilo se načíst grafy z team složky")
                    return

                print(f"📊 Nalezeno {len(team_charts)} grafů v team složce")

                # Filtrování podle ID průzkumu v názvu
                matching_charts = []
                for chart in team_charts:
                    chart_title = chart.get('title', '')

                    # Hledáme grafy, které obsahují kód otázky z průzkumu (G1Q, G2Q, G3Q, atd.)
                    survey_patterns = [f"G{i}Q" for i in range(1, 10)]  # G1Q, G2Q, G3Q, ...

                    if any(pattern in chart_title for pattern in survey_patterns):
                        matching_charts.append(chart)
                        print(f"   ✅ {chart.get('id')}: {chart_title[:80]}...")

                if not matching_charts:
                    print(f"❌ Nenalezeny žádné grafy s kódy otázek (G1Q, G2Q, atd.)")
                    return

                print(f"\n✅ Nalezeno {len(matching_charts)} grafů s kódy otázek")

                # Pokračujeme se staženými grafy
                survey_folder_id = "team_search"  # Označíme, že používáme team přístup

        if survey_folder_id not in ["alternative_search", "team_search"]:
            print(f"✅ Nalezena složka průzkumu: {survey_folder_id}")

            # Získání grafů ze složky
            print(f"🔍 Načítání grafů ze složky {survey_folder_id}...")
            matching_charts = dw.get_charts_in_folder(survey_folder_id)

            if not matching_charts:
                print(f"❌ Ve složce {survey_folder_id} nejsou žádné grafy")
                return

            print(f"✅ Nalezeno {len(matching_charts)} grafů ve složce")
        # else: matching_charts už jsou nastaveny z alternativního/team přístupu

        # Stažení grafů
        downloaded = 0
        failed = 0

        for i, chart in enumerate(matching_charts, 1):
            chart_id = chart['id']
            chart_title = chart.get('title', f'Graf_{chart_id}')

            print(f"\n📊 [{i}/{len(matching_charts)}] Stahuji: {chart_title}")

            try:
                # Export PNG
                png_data = dw.export_chart(
                    chart_id=chart_id,
                    export_format='png',
                    target_width=png_width,
                    border_width=png_border,
                    zoom=png_scale,
                    plain=not full_header_footer
                )

                if png_data:
                    # Vytvoření bezpečného názvu souboru
                    safe_title = re.sub(r'[<>:"/\\|?*]', '_', chart_title)
                    if len(safe_title) > 100:
                        safe_title = safe_title[:100] + "..."

                    png_filename = f"{chart_id}_{safe_title}.png"
                    png_path = os.path.join(charts_dir, png_filename)

                    # Uložení PNG
                    with open(png_path, 'wb') as f:
                        f.write(png_data)

                    print(f"   ✅ Uloženo: {png_filename}")
                    downloaded += 1
                else:
                    print(f"   ❌ Nepodařilo se exportovat graf {chart_id}")
                    failed += 1

            except Exception as e:
                print(f"   ❌ Chyba při stahování {chart_id}: {str(e)}")
                failed += 1

        # Shrnutí
        print(f"\n📊 Shrnutí stahování:")
        print(f"   ✅ Úspěšně staženo: {downloaded} grafů")
        print(f"   ❌ Selhalo: {failed} grafů")
        print(f"   📁 Adresář: {charts_dir}")

        if downloaded > 0:
            print(f"\n🎉 Grafy byly úspěšně staženy do složky: {charts_dir}")

    except ImportError:
        print("❌ Chyba: Datawrapper klient není dostupný")
    except Exception as e:
        print(f"❌ Chyba při stahování grafů: {str(e)}")

def display_log():
    """Zobrazení logu průzkumu"""
    from logger import get_logger
    try:
        logger = get_logger('limwrapp')
        if not logger.handlers:
            print("Žádné logy k zobrazení")
            return
            
        print("\nLogy průzkumu:")
        for handler in logger.handlers:
            if hasattr(handler, 'logs'):
                for log in handler.logs:
                    print(f"- {log}")
    except Exception as e:
        print(f"Chyba při načítání logů: {str(e)}")

def main():
    while True:
        display_menu()
        try:
            choice = input("Vyberte volbu: ")
        except EOFError:
            print("Ukončuji program (EOF)")
            break
        if choice == "0":
            print("Ukončuji program")
            break
        elif choice == "1":
            list_surveys()
        elif choice == "2":
            load_survey_data()
        elif choice == "3":
            load_survey_structure()
        elif choice == "4":
            merge_csv_json()
        elif choice == "5":
            transform_to_long()
        elif choice == "6":
            generate_chart_structure()
        elif choice == "7":
            generate_charts_menu()
        elif choice == "8":
            generate_enhanced_charts_menu()
        elif choice == "9":
            download_png_charts()
        elif choice == "99":
            display_log()
        else:
            print("Neplatná volba")

if __name__ == "__main__":
    main()
