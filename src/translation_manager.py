#!/usr/bin/env python3
"""
Translation Manager - <PERSON><PERSON><PERSON><PERSON><PERSON> př<PERSON> a úprav názvů pro grafy
"""

import json
import os
from typing import Dict, Any, List
from logger import get_logger

logger = get_logger(__name__)

class TranslationManager:
    """Spr<PERSON><PERSON><PERSON> překladů a úprav názvů"""
    
    def __init__(self, survey_id: str):
        self.survey_id = survey_id
        self.survey_dir = f"data/{survey_id}"
        self.translation_file = os.path.join(self.survey_dir, "translations.json")
        self.translations = self._load_translations()
    
    def _load_translations(self) -> Dict[str, Any]:
        """Načte existující překlady nebo vytvoří prázdný slovník"""
        if os.path.exists(self.translation_file):
            try:
                with open(self.translation_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Chyba při načí<PERSON>án<PERSON> p<PERSON>: {e}")
        
        return {
            "metadata": {
                "survey_id": self.survey_id,
                "created": "auto-generated",
                "version": "1.0"
            },
            "question_names": {},      # Názvy otázek
            "question_texts": {},      # Texty otázek  
            "subquestions": {},        # Podotázky
            "response_labels": {},     # Odpovědi (Ano/Ne, škály)
            "chart_titles": {}         # Vlastní názvy grafů
        }
    
    def save_translations(self) -> bool:
        """Uloží překlady do souboru"""
        try:
            os.makedirs(self.survey_dir, exist_ok=True)
            with open(self.translation_file, 'w', encoding='utf-8') as f:
                json.dump(self.translations, f, ensure_ascii=False, indent=2)
            logger.info(f"Překlady uloženy do {self.translation_file}")
            return True
        except Exception as e:
            logger.error(f"Chyba při ukládání překladů: {e}")
            return False
    
    def extract_translatable_strings(self, chart_data_path: str) -> Dict[str, List[str]]:
        """Extrahuje všechny přeložitelné řetězce z chart_data.json"""
        try:
            with open(chart_data_path, 'r', encoding='utf-8') as f:
                chart_data = json.load(f)
            
            strings = {
                "question_names": set(),
                "subquestions": set(), 
                "response_labels": set()
            }
            
            for item in chart_data:
                # Názvy otázek
                if 'name' in item:
                    strings["question_names"].add(item['name'])
                
                # Data podle typu
                if item.get('type') == 'array' and 'data' in item:
                    for data_item in item['data']:
                        # Podotázky
                        if 'subquestion' in data_item:
                            strings["subquestions"].add(data_item['subquestion'])
                        
                        # Odpovědi
                        if 'responses' in data_item:
                            for response_label in data_item['responses'].keys():
                                strings["response_labels"].add(response_label)
                
                elif 'data' in item:
                    for data_item in item['data']:
                        # Labels pro jiné typy otázek
                        if 'label' in data_item:
                            strings["response_labels"].add(data_item['label'])
            
            # Převod na seznamy
            result = {k: sorted(list(v)) for k, v in strings.items()}
            logger.info(f"Extrahováno: {len(result['question_names'])} názvů otázek, "
                       f"{len(result['subquestions'])} podotázek, "
                       f"{len(result['response_labels'])} odpovědí")
            
            return result
            
        except Exception as e:
            logger.error(f"Chyba při extrakci řetězců: {e}")
            return {"question_names": [], "subquestions": [], "response_labels": []}
    
    def generate_translation_template(self, chart_data_path: str) -> bool:
        """Vygeneruje šablonu pro překlad z chart_data.json"""
        try:
            strings = self.extract_translatable_strings(chart_data_path)
            
            # Přidáme nové řetězce do překladů (pokud ještě nejsou)
            for category, items in strings.items():
                if category not in self.translations:
                    self.translations[category] = {}
                
                for item in items:
                    if item not in self.translations[category]:
                        self.translations[category][item] = item  # Výchozí = stejný text
            
            return self.save_translations()
            
        except Exception as e:
            logger.error(f"Chyba při generování šablony: {e}")
            return False
    
    def apply_translations(self, chart_data_path: str, output_path: str = None) -> bool:
        """Aplikuje překlady na chart_data.json"""
        try:
            if output_path is None:
                output_path = chart_data_path.replace('.json', '_translated.json')
            
            with open(chart_data_path, 'r', encoding='utf-8') as f:
                chart_data = json.load(f)
            
            # Aplikace překladů
            for item in chart_data:
                # Názvy otázek
                if 'name' in item and item['name'] in self.translations.get('question_names', {}):
                    item['name'] = self.translations['question_names'][item['name']]
                
                # Data podle typu
                if item.get('type') == 'array' and 'data' in item:
                    for data_item in item['data']:
                        # Podotázky
                        if 'subquestion' in data_item:
                            original = data_item['subquestion']
                            if original in self.translations.get('subquestions', {}):
                                data_item['subquestion'] = self.translations['subquestions'][original]
                        
                        # Odpovědi - musíme přejmenovat klíče
                        if 'responses' in data_item:
                            new_responses = {}
                            for response_label, count in data_item['responses'].items():
                                translated_label = self.translations.get('response_labels', {}).get(response_label, response_label)
                                new_responses[translated_label] = count
                            data_item['responses'] = new_responses
                
                elif 'data' in item:
                    for data_item in item['data']:
                        # Labels pro jiné typy otázek
                        if 'label' in data_item:
                            original = data_item['label']
                            if original in self.translations.get('response_labels', {}):
                                data_item['label'] = self.translations['response_labels'][original]
            
            # Uložení přeloženého souboru
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(chart_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Přeložená data uložena do {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Chyba při aplikaci překladů: {e}")
            return False
    
    def get_translation_stats(self) -> Dict[str, int]:
        """Vrátí statistiky překladů"""
        stats = {}
        for category in ['question_names', 'subquestions', 'response_labels']:
            category_data = self.translations.get(category, {})
            total = len(category_data)
            translated = sum(1 for k, v in category_data.items() if k != v)
            stats[category] = {"total": total, "translated": translated}
        
        return stats
