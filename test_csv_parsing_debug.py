#!/usr/bin/env python3
"""
Test pro debug parsování CSV - problém s detek<PERSON><PERSON> submitdate
"""

import sys
import os
sys.path.append('src')

import pandas as pd

def test_csv_parsing():
    """Test parsování CSV pro debug submitdate"""
    
    survey_id = "827822"
    csv_path = f"src/data/{survey_id}/responses.csv"
    
    print("🔍 Debug parsování CSV")
    print("=" * 60)
    
    # Načtení CSV
    print("📊 Načítání CSV s různými separátory:")
    
    # Test 1: Separator ;
    try:
        df_semicolon = pd.read_csv(csv_path, sep=';')
        print(f"   Separator ';': {len(df_semicolon)} řádků, {len(df_semicolon.columns)} sloupců")
        print(f"   Sloupce: {list(df_semicolon.columns[:5])}...")
        
        if 'submitdate' in df_semicolon.columns:
            completed = df_semicolon[df_semicolon['submitdate'].notna() & (df_semicolon['submitdate'] != '')]
            print(f"   Dokončené záznamy: {len(completed)}")
            print(f"   Příklad submitdate: {df_semicolon['submitdate'].iloc[-3:]}")
        else:
            print("   ❌ Sloupec 'submitdate' nenalezen!")
            
    except Exception as e:
        print(f"   ❌ Chyba při parsování s ';': {e}")
    
    # Test 2: Separator ,
    try:
        df_comma = pd.read_csv(csv_path, sep=',')
        print(f"   Separator ',': {len(df_comma)} řádků, {len(df_comma.columns)} sloupců")
        print(f"   Sloupce: {list(df_comma.columns[:5])}...")
        
    except Exception as e:
        print(f"   ❌ Chyba při parsování s ',': {e}")
    
    # Test 3: Auto-detect
    try:
        df_auto = pd.read_csv(csv_path)
        print(f"   Auto-detect: {len(df_auto)} řádků, {len(df_auto.columns)} sloupců")
        print(f"   Sloupce: {list(df_auto.columns[:5])}...")
        
    except Exception as e:
        print(f"   ❌ Chyba při auto-detect: {e}")
    
    print("\n🔍 Detailní analýza správného parsování:")
    if 'df_semicolon' in locals():
        print(f"   Celkem řádků: {len(df_semicolon)}")
        print(f"   Sloupce obsahující 'submit': {[col for col in df_semicolon.columns if 'submit' in col.lower()]}")
        print(f"   Sloupce obsahující 'date': {[col for col in df_semicolon.columns if 'date' in col.lower()]}")
        
        # Analýza submitdate hodnot
        if 'submitdate' in df_semicolon.columns:
            print(f"\n📅 Analýza submitdate:")
            print(f"   Celkem hodnot: {len(df_semicolon['submitdate'])}")
            print(f"   Prázdné hodnoty: {df_semicolon['submitdate'].isna().sum()}")
            print(f"   Prázdné stringy: {(df_semicolon['submitdate'] == '').sum()}")
            print(f"   Vyplněné hodnoty: {((df_semicolon['submitdate'].notna()) & (df_semicolon['submitdate'] != '')).sum()}")
            
            # Ukázka posledních hodnot
            print(f"   Posledních 5 hodnot:")
            for i, val in enumerate(df_semicolon['submitdate'].tail(5)):
                print(f"     [{len(df_semicolon)-5+i}]: '{val}'")

if __name__ == "__main__":
    test_csv_parsing()
