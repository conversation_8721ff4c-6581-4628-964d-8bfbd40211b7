#!/usr/bin/env python3
"""
Test správného řazení dat před odesláním do Datawrapper
"""

import sys
import os
import pandas as pd

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_array_data_ordering():
    """Test řazení dat pro array otázky podle LSS order"""
    print("🧪 Test řazení dat pro array otázky...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        # Simulace LSS struktury
        mock_lss_structure = {
            "G1": {
                "questions": {
                    "G1Q00001": {
                        "answeroptions": {
                            "AO01": {
                                "answer": "rozhodně ano",
                                "order": 0
                            },
                            "AO02": {
                                "answer": "spíše ano", 
                                "order": 1
                            },
                            "AO03": {
                                "answer": "spíše ne",
                                "order": 2
                            },
                            "AO04": {
                                "answer": "rozhodně ne",
                                "order": 3
                            },
                            "AO05": {
                                "answer": "neumím to posoudit",
                                "order": 4
                            }
                        }
                    }
                }
            }
        }
        
        generator = EnhancedChartGenerator()
        generator.lss_structure = mock_lss_structure
        
        # Testovací data - labels v abecedním pořadí (špatně)
        test_chart_data = {
            'data_type': 'array_scale',
            'categories': ['Ministerstva zpravidla nevyvíjejí dostatek úsilí ke shromáždění a vyhodnocení relevantních dat'],
            'data': [
                {'category': 'Ministerstva zpravidla nevyvíjejí dostatek úsilí ke shromáždění a vyhodnocení relevantních dat', 'label': 'neumím to posoudit', 'value': 19},
                {'category': 'Ministerstva zpravidla nevyvíjejí dostatek úsilí ke shromáždění a vyhodnocení relevantních dat', 'label': 'rozhodně ano', 'value': 18},
                {'category': 'Ministerstva zpravidla nevyvíjejí dostatek úsilí ke shromáždění a vyhodnocení relevantních dat', 'label': 'rozhodně ne', 'value': 22},
                {'category': 'Ministerstva zpravidla nevyvíjejí dostatek úsilí ke shromáždění a vyhodnocení relevantních dat', 'label': 'spíše ano', 'value': 63},
                {'category': 'Ministerstva zpravidla nevyvíjejí dostatek úsilí ke shromáždění a vyhodnocení relevantních dat', 'label': 'spíše ne', 'value': 31}
            ]
        }
        
        print("✅ Testovací data připravena")
        
        # Test přípravy dat
        df_result = generator._prepare_array_data_for_dw(test_chart_data, "G1Q00001")
        
        print(f"✅ Výsledný DataFrame:")
        print(df_result)
        
        # Ověření pořadí sloupců
        columns = list(df_result.columns)
        expected_order = ['Podotázka', 'rozhodně ano', 'spíše ano', 'spíše ne', 'rozhodně ne', 'neumím to posoudit']
        
        print(f"✅ Pořadí sloupců: {columns}")
        print(f"✅ Očekávané pořadí: {expected_order}")
        
        if columns == expected_order:
            print("✅ Pořadí sloupců je správné podle LSS order!")
            
            # Ověření hodnot
            row = df_result.iloc[0]
            print(f"✅ Hodnoty v řádku:")
            for col in columns[1:]:  # Přeskočíme 'Podotázka'
                print(f"   {col}: {row[col]}")
            
            return True
        else:
            print("❌ Pořadí sloupců není správné")
            print(f"   Očekáváno: {expected_order}")
            print(f"   Získáno: {columns}")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_without_lss():
    """Test chování bez LSS struktury"""
    print("\n🧪 Test fallback bez LSS struktury...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        generator = EnhancedChartGenerator()
        # Bez LSS struktury
        
        test_chart_data = {
            'data_type': 'array_scale',
            'categories': ['Test kategorie'],
            'data': [
                {'category': 'Test kategorie', 'label': 'c', 'value': 1},
                {'category': 'Test kategorie', 'label': 'a', 'value': 2},
                {'category': 'Test kategorie', 'label': 'b', 'value': 3}
            ]
        }
        
        df_result = generator._prepare_array_data_for_dw(test_chart_data, "neexistuje")
        
        # Mělo by použít původní pořadí (set -> list -> sort)
        columns = list(df_result.columns)
        print(f"✅ Sloupce bez LSS: {columns}")
        
        # Ověříme, že fallback funguje
        if len(columns) > 1:  # Podotázka + nějaké sloupce
            print("✅ Fallback funguje - data byla zpracována")
            return True
        else:
            print("❌ Fallback nefunguje")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu fallback: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test řazení dat před odesláním do Datawrapper")
    print("=" * 60)
    
    tests = [
        test_array_data_ordering,
        test_fallback_without_lss
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Všechny testy prošly! Řazení dat podle LSS order funguje.")
        print("\n📋 Oprava:")
        print("   • Data se nyní řadí podle LSS order místo abecedně")
        print("   • Sloupce v Datawrapper budou ve správném pořadí")
        print("   • Škálové hodnoty budou logicky seřazené")
        return True
    else:
        print("❌ Některé testy selhaly. Zkontrolujte implementaci.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
