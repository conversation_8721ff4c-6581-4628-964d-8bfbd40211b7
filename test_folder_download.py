#!/usr/bin/env python3
"""
Test pro ově<PERSON><PERSON>í stahování grafů ze složek Datawrapper
"""

import sys
import os
from unittest.mock import Mock, patch

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_folder_contents_api():
    """Test API pro získání obsahu složky"""
    print("🧪 Test API pro obsah složky...")
    
    try:
        from datawrapper_client import DatawrapperClient
        
        # Simulace odpovědi API pro obsah složky
        mock_folder_response = {
            "children": [
                {
                    "id": "329553",
                    "type": "folder",
                    "title": "827822",
                    "createdAt": "2024-01-01T00:00:00.000Z"
                },
                {
                    "id": "abc123",
                    "type": "chart",
                    "title": "Graf 1",
                    "createdAt": "2024-01-01T00:00:00.000Z"
                },
                {
                    "id": "def456", 
                    "type": "chart",
                    "title": "Graf 2",
                    "createdAt": "2024-01-01T00:00:00.000Z"
                }
            ]
        }
        
        with patch('requests.get') as mock_get:
            mock_get.return_value.status_code = 200
            mock_get.return_value.json.return_value = mock_folder_response
            
            dw = DatawrapperClient()
            contents = dw.get_folder_contents("329499")
            
            print(f"✅ Získáno {len(contents)} položek ze složky")
            
            # Rozdělení na složky a grafy
            folders = [item for item in contents if item.get('type') == 'folder']
            charts = [item for item in contents if item.get('type') == 'chart']
            
            print(f"   📁 Složky: {len(folders)}")
            for folder in folders:
                print(f"      - {folder['id']}: {folder['title']}")
            
            print(f"   📊 Grafy: {len(charts)}")
            for chart in charts:
                print(f"      - {chart['id']}: {chart['title']}")
            
            if len(folders) == 1 and len(charts) == 2:
                print("✅ Správně rozpoznány složky a grafy")
                return True
            else:
                print("❌ Nesprávné rozpoznání typů položek")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu obsahu složky: {str(e)}")
        return False

def test_subfolder_search():
    """Test hledání podsložky podle názvu"""
    print("\n🧪 Test hledání podsložky...")
    
    try:
        from datawrapper_client import DatawrapperClient
        
        # Simulace obsahu nadřazené složky
        mock_contents = [
            {"id": "329553", "type": "folder", "title": "827822"},
            {"id": "329554", "type": "folder", "title": "861999"},
            {"id": "abc123", "type": "chart", "title": "Nějaký graf"}
        ]
        
        with patch.object(DatawrapperClient, 'get_folder_contents', return_value=mock_contents):
            dw = DatawrapperClient()
            
            # Test hledání existující složky
            folder_id = dw.find_subfolder_by_name("329499", "827822")
            print(f"✅ Hledání '827822': {folder_id}")
            
            if folder_id == "329553":
                print("✅ Správně nalezena složka 827822")
            else:
                print(f"❌ Nesprávné ID složky: {folder_id}")
                return False
            
            # Test hledání neexistující složky
            missing_folder = dw.find_subfolder_by_name("329499", "999999")
            print(f"✅ Hledání '999999': {missing_folder}")
            
            if missing_folder is None:
                print("✅ Správně vráceno None pro neexistující složku")
                return True
            else:
                print(f"❌ Mělo být vráceno None, ale vráceno: {missing_folder}")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu hledání složky: {str(e)}")
        return False

def test_charts_in_folder():
    """Test získání grafů ze složky"""
    print("\n🧪 Test získání grafů ze složky...")
    
    try:
        from datawrapper_client import DatawrapperClient
        
        # Simulace obsahu složky s grafy
        mock_folder_contents = [
            {"id": "chart1", "type": "chart", "title": "Graf 1 - Otázka A"},
            {"id": "chart2", "type": "chart", "title": "Graf 2 - Otázka B"},
            {"id": "subfolder", "type": "folder", "title": "Podsložka"},
            {"id": "chart3", "type": "chart", "title": "Graf 3 - Otázka C"}
        ]
        
        with patch.object(DatawrapperClient, 'get_folder_contents', return_value=mock_folder_contents):
            dw = DatawrapperClient()
            charts = dw.get_charts_in_folder("329553")
            
            print(f"✅ Nalezeno {len(charts)} grafů")
            for chart in charts:
                print(f"   📊 {chart['id']}: {chart['title']}")
            
            # Ověření, že jsou vráceny pouze grafy (ne složky)
            chart_ids = [chart['id'] for chart in charts]
            expected_ids = ['chart1', 'chart2', 'chart3']
            
            if chart_ids == expected_ids:
                print("✅ Správně filtrovány pouze grafy")
                return True
            else:
                print(f"❌ Nesprávné ID grafů: {chart_ids}")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu grafů ze složky: {str(e)}")
        return False

def test_config_loading():
    """Test načítání Datawrapper konfigurace"""
    print("\n🧪 Test načítání konfigurace...")
    
    try:
        from config_loader import load_config
        
        # Mock environment variables
        with patch.dict(os.environ, {
            'DATAWRAPPER_TEAM_ID': '57Zj-Xbm',
            'DATAWRAPPER_LIMESURVEY_FOLDER_ID': '329499'
        }):
            config = load_config()
            
            team_id = config.get('DATAWRAPPER_TEAM_ID')
            folder_id = config.get('DATAWRAPPER_LIMESURVEY_FOLDER_ID')
            
            print(f"✅ Team ID: {team_id}")
            print(f"✅ Folder ID: {folder_id}")
            
            if team_id == '57Zj-Xbm' and folder_id == '329499':
                print("✅ Konfigurace načtena správně")
                return True
            else:
                print("❌ Nesprávná konfigurace")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu konfigurace: {str(e)}")
        return False

def test_complete_workflow():
    """Test kompletního workflow"""
    print("\n🧪 Test kompletního workflow...")
    
    try:
        # Simulace celého procesu
        survey_id = "827822"
        parent_folder_id = "329499"
        expected_survey_folder_id = "329553"
        
        print(f"✅ 1. Zadáno ID průzkumu: {survey_id}")
        print(f"✅ 2. Nadřazená složka: {parent_folder_id}")
        print(f"✅ 3. Hledání složky '{survey_id}'...")
        print(f"✅ 4. Nalezena složka: {expected_survey_folder_id}")
        print(f"✅ 5. Načítání grafů ze složky...")
        print(f"✅ 6. Stahování PNG souborů...")
        print(f"✅ 7. Uložení do charts/{survey_id}/")
        
        print("✅ Kompletní workflow je připraven")
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testu workflow: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test stahování grafů ze složek Datawrapper")
    print("=" * 60)
    
    tests = [
        test_folder_contents_api,
        test_subfolder_search,
        test_charts_in_folder,
        test_config_loading,
        test_complete_workflow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Všechny testy prošly! Stahování ze složek je připraveno.")
        print("\n📋 Nový workflow:")
        print("   1. Zadání ID průzkumu (827822)")
        print("   2. Hledání složky v DATAWRAPPER_LIMESURVEY_FOLDER_ID")
        print("   3. Nalezení podsložky s názvem průzkumu")
        print("   4. Načtení všech grafů z této složky")
        print("   5. Stažení jako PNG s parametry")
        print("   6. Uložení do charts/ID_průzkumu/")
        return True
    else:
        print("❌ Některé testy selhaly. Zkontrolujte implementaci.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
