#!/usr/bin/env python3
"""
Test počtu grafů z /folders API
"""

import sys
import os

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_folders_count():
    """Test počtu grafů z folders API"""
    print("🧪 Test počtu grafů z /folders API...")
    
    try:
        from datawrapper_client import DatawrapperClient
        
        dw = DatawrapperClient()
        
        # Získáme debug data
        debug_data = dw.debug_all_folders()
        
        total_charts = 0
        team_charts = 0
        user_charts = 0
        
        if 'folders' in debug_data and 'data' in debug_data['folders']:
            data = debug_data['folders']['data']
            
            if 'list' in data:
                for item in data['list']:
                    item_type = item.get('type')
                    charts = item.get('charts', [])
                    
                    print(f"\n📊 {item_type.upper()}: {item.get('name', item.get('id'))}")
                    print(f"   Grafů: {len(charts)}")
                    
                    total_charts += len(charts)
                    
                    if item_type == 'team':
                        team_charts += len(charts)
                        
                        # Zobrazíme prvních 5 grafů
                        for i, chart in enumerate(charts[:5]):
                            title = chart.get('title', 'Bez názvu')
                            print(f"   {i+1}. {chart.get('id')}: {title[:50]}...")
                        
                        if len(charts) > 5:
                            print(f"   ... a dalších {len(charts) - 5} grafů")
                            
                    elif item_type == 'user':
                        user_charts += len(charts)
        
        print(f"\n📊 SHRNUTÍ:")
        print(f"   Celkem grafů: {total_charts}")
        print(f"   Team grafů: {team_charts}")
        print(f"   User grafů: {user_charts}")
        
        return total_charts
        
    except Exception as e:
        print(f"❌ Chyba: {str(e)}")
        return 0

def main():
    """Hlavní funkce"""
    print("🚀 Test počtu grafů z Datawrapper API")
    print("=" * 50)
    
    count = test_folders_count()
    
    print("\n" + "=" * 50)
    print(f"📊 Celkový počet grafů: {count}")

if __name__ == "__main__":
    main()
