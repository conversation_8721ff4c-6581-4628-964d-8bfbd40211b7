#!/usr/bin/env python3
"""
Test systému spr<PERSON>vy jaz<PERSON> pro grafy
"""

import sys
import os
import json
import tempfile

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_language_settings():
    """Test nastavení jazyka v TranslationManager"""
    print("🧪 Test nastavení jazyka...")
    
    try:
        from translation_manager import TranslationManager
        
        # Vytvoření dočasného ad<PERSON>ře
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_dir = os.path.join(temp_dir, "827822")
            os.makedirs(survey_dir, exist_ok=True)
            
            # Vytvoření TranslationManager
            tm = TranslationManager("827822")
            tm.survey_dir = survey_dir
            tm.translation_file = os.path.join(survey_dir, "translations.json")
            
            # Test výchozího jazyka
            default_language = tm.get_chart_language()
            print(f"✅ Výchozí jazyk: {default_language}")
            
            if default_language != "cs-CZ":
                print("❌ Výchozí jazyk není čeština")
                return False
            
            # Test dostupných jazyků
            available_languages = tm.get_available_languages()
            print(f"✅ Dostupné jazyky: {list(available_languages.keys())}")
            
            expected_languages = ["cs-CZ", "en-US", "en-GB", "de-DE", "fr-FR", "es-ES"]
            for lang in expected_languages:
                if lang not in available_languages:
                    print(f"❌ Chybí jazyk: {lang}")
                    return False
            
            # Test nastavení jazyka
            test_language = "en-US"
            if tm.set_chart_language(test_language):
                print(f"✅ Jazyk nastaven na: {test_language}")
                
                # Ověření změny
                current_language = tm.get_chart_language()
                if current_language == test_language:
                    print("✅ Změna jazyka úspěšně ověřena")
                    return True
                else:
                    print(f"❌ Jazyk se nezměnil: {current_language}")
                    return False
            else:
                print("❌ Nepodařilo se nastavit jazyk")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_language_in_datawrapper_payload():
    """Test přidání jazyka do Datawrapper API payload"""
    print("\n🧪 Test jazyka v Datawrapper API...")
    
    try:
        from datawrapper_client import DatawrapperClient
        import json
        
        # Mock test - ověříme, že create_chart přijímá language parametr
        client = DatawrapperClient()
        
        # Test, že metoda přijímá language parametr
        import inspect
        signature = inspect.signature(client.create_chart)
        
        if 'language' in signature.parameters:
            print("✅ Metoda create_chart podporuje language parametr")
            
            # Test výchozí hodnoty
            language_param = signature.parameters['language']
            if language_param.default is None:
                print("✅ Language parametr má správnou výchozí hodnotu (None)")
                return True
            else:
                print(f"⚠️  Language parametr má neočekávanou výchozí hodnotu: {language_param.default}")
                return False
        else:
            print("❌ Metoda create_chart nepodporuje language parametr")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        return False

def test_enhanced_chart_generator_language():
    """Test jazyka v EnhancedChartGenerator"""
    print("\n🧪 Test jazyka v EnhancedChartGenerator...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        import inspect
        
        # Test, že konstruktor přijímá language parametr
        signature = inspect.signature(EnhancedChartGenerator.__init__)
        
        if 'language' in signature.parameters:
            print("✅ EnhancedChartGenerator podporuje language parametr")
            
            # Test výchozí hodnoty
            language_param = signature.parameters['language']
            if language_param.default == "cs-CZ":
                print("✅ Language parametr má správnou výchozí hodnotu (cs-CZ)")
                
                # Test vytvoření instance
                generator = EnhancedChartGenerator(language="en-US")
                if hasattr(generator, 'language') and generator.language == "en-US":
                    print("✅ Language se správně nastavuje v instanci")
                    return True
                else:
                    print("❌ Language se nenastavuje správně v instanci")
                    return False
            else:
                print(f"❌ Language parametr má špatnou výchozí hodnotu: {language_param.default}")
                return False
        else:
            print("❌ EnhancedChartGenerator nepodporuje language parametr")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        return False

def test_language_integration():
    """Test integrace jazyka v celém systému"""
    print("\n🧪 Test integrace jazyka...")
    
    try:
        from translation_manager import TranslationManager
        from enhanced_chart_generator import EnhancedChartGenerator
        
        # Vytvoření dočasného adresáře
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_dir = os.path.join(temp_dir, "827822")
            os.makedirs(survey_dir, exist_ok=True)
            
            # Nastavení jazyka přes TranslationManager
            tm = TranslationManager("827822")
            tm.survey_dir = survey_dir
            tm.translation_file = os.path.join(survey_dir, "translations.json")
            
            # Nastavení na angličtinu
            tm.set_chart_language("en-US")
            
            # Načtení jazyka
            chart_language = tm.get_chart_language()
            print(f"✅ Jazyk z TranslationManager: {chart_language}")
            
            # Vytvoření generátoru s tímto jazykem
            generator = EnhancedChartGenerator(language=chart_language)
            
            if generator.language == "en-US":
                print("✅ Jazyk se správně předává do generátoru")
                return True
            else:
                print(f"❌ Jazyk se nepředává správně: {generator.language}")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu integrace: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test systému správy jazyků pro grafy")
    print("=" * 60)
    
    tests = [
        test_language_settings,
        test_language_in_datawrapper_payload,
        test_enhanced_chart_generator_language,
        test_language_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Systém správy jazyků funguje!")
        print("\n📋 Co bylo implementováno:")
        print("   • Výchozí jazyk: čeština (cs-CZ)")
        print("   • Menu 10 → Volba 5: Nastavení jazyka grafů")
        print("   • 6 podporovaných jazyků (cs-CZ, en-US, en-GB, de-DE, fr-FR, es-ES)")
        print("   • Automatické načítání jazyka při generování grafů")
        print("   • Předávání jazyka do Datawrapper API")
        print("\n🎯 Jak použít:")
        print("   1. Menu 10 → Volba 5 (nastavit jazyk)")
        print("   2. Vyberte požadovaný jazyk")
        print("   3. Menu 8 (generování grafů) - automaticky použije nastavený jazyk")
        print("   4. Grafy budou mít patičku v nastaveném jazyce")
        return True
    else:
        print("❌ Některé části systému jazyků nefungují.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
