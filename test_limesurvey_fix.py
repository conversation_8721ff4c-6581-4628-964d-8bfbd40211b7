#!/usr/bin/env python3
"""
Test opravy LimeSurvey API chyb 500
"""

import sys
import os

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_limesurvey_structure():
    """Test získání struktury průzkumu bez chyb 500"""
    print("🧪 Test získání struktury LimeSurvey průzkumu...")
    
    try:
        from limesurvey_client import LimeSurveyClient
        
        # Inicializace klienta
        client = LimeSurveyClient()
        
        # Test survey ID
        survey_id = "827822"
        
        print(f"✅ Získávám strukturu průzkumu: {survey_id}")
        
        # Pokus o získání struktury
        result = client.get_survey_structure_via_api(survey_id)
        
        if result:
            print(f"✅ Struktura úspěšně získána: {result}")
            
            # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že soubor existuje
            if os.path.exists(result):
                print(f"✅ Soubor struktury existuje: {os.path.getsize(result)} bytů")
                
                # Načtení a kontrola obsahu
                import json
                with open(result, 'r', encoding='utf-8') as f:
                    structure = json.load(f)
                
                print(f"✅ Struktura obsahuje:")
                print(f"   - Survey ID: {structure.get('survey_id')}")
                print(f"   - Počet skupin: {len(structure.get('groups', []))}")
                
                total_questions = sum(len(g.get('questions', [])) for g in structure.get('groups', []))
                print(f"   - Celkem otázek: {total_questions}")
                
                # Kontrola, že otázky mají vlastnosti
                has_properties = False
                for group in structure.get('groups', []):
                    for question in group.get('questions', []):
                        if question.get('properties'):
                            has_properties = True
                            break
                    if has_properties:
                        break
                
                if has_properties:
                    print("✅ Otázky obsahují vlastnosti")
                    return True
                else:
                    print("⚠️  Otázky neobsahují vlastnosti")
                    return False
            else:
                print(f"❌ Soubor struktury neexistuje: {result}")
                return False
        else:
            print("❌ Nepodařilo se získat strukturu")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_limesurvey_session():
    """Test LimeSurvey session"""
    print("\n🧪 Test LimeSurvey session...")
    
    try:
        from limesurvey_client import LimeSurveyClient
        
        client = LimeSurveyClient()
        
        # Test session key
        session_key = client.get_session_key()
        
        if session_key:
            print(f"✅ Session key získán: {session_key[:10]}...")
            
            # Test uvolnění session
            client.release_session()
            print("✅ Session uvolněn")
            
            return True
        else:
            print("❌ Nepodařilo se získat session key")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu session: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test opravy LimeSurvey API chyb 500")
    print("=" * 60)
    
    tests = [
        test_limesurvey_session,
        test_limesurvey_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Oprava LimeSurvey API funguje!")
        print("\n📋 Co bylo opraveno:")
        print("   • Odstraněno problematické volání get_question_properties")
        print("   • Vlastnosti otázek se získávají z list_questions")
        print("   • Přidáno lepší error handling")
        print("   • Žádné chyby 500 při načítání struktury")
        return True
    else:
        print("❌ Některé testy selhaly.")
        print("\n🔧 Možná řešení:")
        print("   • Zkontrolovat LimeSurvey API přihlašovací údaje")
        print("   • Ověřit dostupnost LimeSurvey serveru")
        print("   • Zkontrolovat oprávnění k průzkumu")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
