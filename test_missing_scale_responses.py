#!/usr/bin/env python3
"""
Test opravy extrakce škálových odpovědí a question_texts
"""

import sys
import os
import json
import tempfile

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_scale_response_extraction():
    """Test extrakce škálových odpovědí z array otázek"""
    print("🧪 Test extrakce škálových odpovědí...")
    
    try:
        from translation_manager import TranslationManager
        
        # Vytvoření testovacích dat s array otázkou obsahující škálové odpovědi
        test_chart_data = [
            {
                "code": "G6Q00001",
                "name": "Ztotožňujete s níže uvedenými názory?",
                "type": "array",
                "question_text": "Prosím ohodnoťte následující tvrzení podle míry souhlasu.",
                "data": [
                    {
                        "subquestion": "České vládní instituce uspokojivě informují zainteresovanou veřejnost o legislativě EU.",
                        "responses": {
                            "neumím to posoudit": 9,
                            "rozhodně ano": 3,
                            "rozhodně ne": 10,
                            "spíše ne": 32,
                            "spíše ano": 13
                        }
                    },
                    {
                        "subquestion": "Konzultační nástroje Evropské komise umožňují včasné a efektivní zapojení stakeholderů.",
                        "responses": {
                            "neumím to posoudit": 18,
                            "rozhodně ano": 5,
                            "rozhodně ne": 2,
                            "spíše ne": 26,
                            "spíše ano": 16
                        }
                    }
                ]
            },
            {
                "code": "G1Q00001",
                "name": "Jednoduchá otázka",
                "type": "single_choice",
                "question_text": "Vyberte jednu možnost.",
                "data": [
                    {"label": "Ano", "value": 25},
                    {"label": "Ne", "value": 15}
                ]
            }
        ]
        
        # Vytvoření dočasného adresáře
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_dir = os.path.join(temp_dir, "827822")
            os.makedirs(survey_dir, exist_ok=True)
            
            # Uložení testovacích dat
            chart_data_path = os.path.join(survey_dir, "chart_data.json")
            with open(chart_data_path, 'w', encoding='utf-8') as f:
                json.dump(test_chart_data, f, ensure_ascii=False, indent=2)
            
            # Test extrakce
            tm = TranslationManager("827822")
            tm.survey_dir = survey_dir
            
            strings = tm.extract_translatable_strings(chart_data_path)
            
            print(f"✅ Extrahováno:")
            print(f"   - {len(strings['question_names'])} názvů otázek")
            print(f"   - {len(strings['question_texts'])} textů otázek")
            print(f"   - {len(strings['subquestions'])} podotázek")
            print(f"   - {len(strings['response_labels'])} odpovědí")
            
            # Kontrola škálových odpovědí
            expected_scale_responses = {"rozhodně ano", "spíše ano", "spíše ne", "rozhodně ne", "neumím to posoudit"}
            found_scale_responses = set(strings['response_labels'])
            
            missing_responses = expected_scale_responses - found_scale_responses
            if missing_responses:
                print(f"❌ Chybí škálové odpovědi: {missing_responses}")
                return False
            else:
                print("✅ Všechny škálové odpovědi nalezeny")
            
            # Kontrola question_texts
            if len(strings['question_texts']) >= 2:
                print("✅ Question_texts jsou extrahovány")
                print(f"   Příklad: {list(strings['question_texts'])[0][:50]}...")
            else:
                print("❌ Question_texts nejsou extrahovány")
                return False
            
            # Kontrola podotázek
            if len(strings['subquestions']) >= 2:
                print("✅ Podotázky jsou extrahovány")
                print(f"   Příklad: {list(strings['subquestions'])[0][:50]}...")
            else:
                print("❌ Podotázky nejsou extrahovány")
                return False
            
            return True
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_real_data_extraction():
    """Test na skutečných datech z průzkumu 827822"""
    print("\n🧪 Test na skutečných datech...")
    
    try:
        from translation_manager import TranslationManager
        
        chart_data_path = "data/827822/chart_data.json"
        if not os.path.exists(chart_data_path):
            print("⚠️  Skutečná data neexistují - přeskakuji test")
            return True
        
        tm = TranslationManager("827822")
        strings = tm.extract_translatable_strings(chart_data_path)
        
        print(f"✅ Extrahováno ze skutečných dat:")
        print(f"   - {len(strings['question_names'])} názvů otázek")
        print(f"   - {len(strings['question_texts'])} textů otázek")
        print(f"   - {len(strings['subquestions'])} podotázek")
        print(f"   - {len(strings['response_labels'])} odpovědí")
        
        # Kontrola škálových odpovědí
        scale_responses = {"rozhodně ano", "spíše ano", "spíše ne", "rozhodně ne", "neumím to posoudit"}
        found_scale_responses = set(strings['response_labels']) & scale_responses
        
        if len(found_scale_responses) >= 4:  # Alespoň 4 z 5 škálových odpovědí
            print(f"✅ Nalezeny škálové odpovědi: {found_scale_responses}")
        else:
            print(f"❌ Chybí škálové odpovědi. Nalezeno pouze: {found_scale_responses}")
            return False
        
        # Kontrola, že nejsou prázdné kategorie
        if all(len(strings[cat]) > 0 for cat in ['question_names', 'subquestions', 'response_labels']):
            print("✅ Všechny kategorie obsahují data")
            return True
        else:
            empty_categories = [cat for cat in strings if len(strings[cat]) == 0]
            print(f"❌ Prázdné kategorie: {empty_categories}")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu skutečných dat: {str(e)}")
        return False

def test_template_generation_with_scales():
    """Test generování šablony s škálovými odpověďmi"""
    print("\n🧪 Test generování šablony s škálovými odpověďmi...")
    
    try:
        from translation_manager import TranslationManager
        
        chart_data_path = "data/827822/chart_data.json"
        if not os.path.exists(chart_data_path):
            print("⚠️  Skutečná data neexistují - přeskakuji test")
            return True
        
        # Vytvoření dočasného adresáře
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_dir = os.path.join(temp_dir, "827822")
            os.makedirs(survey_dir, exist_ok=True)
            
            tm = TranslationManager("827822", "en-US")
            tm.survey_dir = survey_dir
            
            # Generování šablony
            if tm.create_translation_template_for_language(chart_data_path, "en-US"):
                translation_file = os.path.join(survey_dir, "translations_en-US.json")
                
                with open(translation_file, 'r', encoding='utf-8') as f:
                    translations = json.load(f)
                
                # Kontrola škálových odpovědí v šabloně
                response_labels = translations.get('response_labels', {})
                scale_responses = {"rozhodně ano", "spíše ano", "spíše ne", "rozhodně ne", "neumím to posoudit"}
                found_in_template = set(response_labels.keys()) & scale_responses
                
                if len(found_in_template) >= 4:
                    print(f"✅ Škálové odpovědi v šabloně: {found_in_template}")
                    
                    # Kontrola question_texts
                    question_texts = translations.get('question_texts', {})
                    if len(question_texts) > 0:
                        print(f"✅ Question_texts v šabloně: {len(question_texts)} položek")
                        return True
                    else:
                        print("❌ Question_texts nejsou v šabloně")
                        return False
                else:
                    print(f"❌ Škálové odpovědi chybí v šabloně. Nalezeno: {found_in_template}")
                    return False
            else:
                print("❌ Nepodařilo se vygenerovat šablonu")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu šablony: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test opravy extrakce škálových odpovědí a question_texts")
    print("=" * 70)
    
    tests = [
        test_scale_response_extraction,
        test_real_data_extraction,
        test_template_generation_with_scales
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 70)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed >= 2:  # Alespoň 2 ze 3 testů
        print("✅ Oprava extrakce škálových odpovědí funguje!")
        print("\n📋 Co bylo opraveno:")
        print("   • Extrakce question_texts z chart_data.json")
        print("   • Lepší extrakce škálových odpovědí (rozhodně ano, spíše ano, atd.)")
        print("   • Aplikace překladů pro question_texts")
        print("   • Statistiky nyní ukazují správné počty")
        print("\n🎯 Nyní by mělo Menu 10 zobrazovat:")
        print("   question_names: X/Y přeloženo")
        print("   question_texts: X/Y přeloženo")
        print("   subquestions: X/Y přeloženo")
        print("   response_labels: X/Y přeloženo (včetně škálových)")
        return True
    else:
        print("❌ Oprava nefunguje správně")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
