#!/usr/bin/env python3
"""
Test nového generování šablon bez safe_to_translate/personal_data
"""

import sys
import os
sys.path.append('src')

from translation_manager import TranslationManager
import json

def test_new_template_generation():
    """Test nového generování šablon"""
    
    survey_id = "827822"
    
    print("🧪 Test nového generování šablon (bez safe/personal)")
    print("=" * 60)
    
    # Změníme do src adresáře
    os.chdir('src')
    
    chart_data_path = f"data/{survey_id}/chart_data.json"
    test_template_path = f"data/{survey_id}/translations_test.json"
    
    # Smazání existující testovací šablony
    if os.path.exists(test_template_path):
        os.remove(test_template_path)
        print(f"🗑️  Smazána existující testovací šablona")
    
    # Vytvoření TranslationManager pro testovací jazyk
    tm = TranslationManager(survey_id)
    tm.current_language = "test"
    tm.translation_file = test_template_path
    tm.translations = tm._load_translations()
    
    print(f"📊 Generování šablony z: {chart_data_path}")
    print(f"📁 Výstup do: {test_template_path}")
    
    # Generování šablony
    success = tm.generate_translation_template(chart_data_path)
    print(f"✅ Generování: {success}")
    
    if success and os.path.exists(test_template_path):
        with open(test_template_path, 'r', encoding='utf-8') as f:
            template_data = json.load(f)
        
        print(f"\n📋 Struktura vygenerované šablony:")
        for key, value in template_data.items():
            if key in ['metadata', 'language_settings']:
                print(f"   {key}: {type(value).__name__}")
            else:
                if isinstance(value, dict):
                    if key == 'open_text_responses':
                        # Počítáme položky ve všech otázkách
                        total_items = sum(len(q_data) for q_data in value.values())
                        print(f"   {key}: {len(value)} otázek, {total_items} položek")
                        
                        # Ukázka struktury
                        if value:
                            first_question = list(value.keys())[0]
                            first_data = value[first_question]
                            print(f"     Příklad {first_question}: {len(first_data)} položek")
                            if first_data:
                                first_item = list(first_data.items())[0]
                                print(f"       '{first_item[0][:50]}...' -> '{first_item[1][:50]}...'")
                    else:
                        print(f"   {key}: {len(value)} položek")
                else:
                    print(f"   {key}: {type(value).__name__}")
        
        # Kontrola, že neobsahuje safe_to_translate/personal_data
        has_safe_personal = False
        def check_structure(obj, path=""):
            nonlocal has_safe_personal
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key in ['safe_to_translate', 'personal_data']:
                        has_safe_personal = True
                        print(f"   ❌ Nalezeno {key} v {path}")
                    check_structure(value, f"{path}.{key}")
        
        check_structure(template_data)
        
        if not has_safe_personal:
            print(f"   ✅ Šablona neobsahuje safe_to_translate/personal_data struktury")
        
        # Smazání testovací šablony
        os.remove(test_template_path)
        print(f"\n🗑️  Testovací šablona smazána")
        
    else:
        print(f"❌ Generování šablony selhalo")
    
    print(f"\n🎯 Test dokončen!")
    
    # Návrat do původního adresáře
    os.chdir('..')

if __name__ == "__main__":
    test_new_template_generation()
