#!/usr/bin/env python3
"""
Test názvu PNG souborů s jazykem pro rozlišení verzí
"""

import sys
import os
import json
import tempfile

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_png_filename_with_language():
    """Test, že PNG soubory mají jazyk v názvu"""
    print("🧪 Test názvu PNG souborů s jazykem...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        # Test českého generátoru
        generator_cs = EnhancedChartGenerator(language="cs-CZ")
        
        # Simulace generování názvu souboru
        question_code = "G1Q00001"
        chart_name = "Uveďte prosím název instituce"
        
        # Simulace logiky z enhanced_chart_generator.py
        safe_name = chart_name.replace(' ', '_').replace('/', '_').replace('\\', '_')
        if len(safe_name) > 100:
            safe_name = safe_name[:100] + "..."
        
        language_suffix = generator_cs.language.replace('-', '_')  # cs-CZ → cs_CZ
        png_filename_cs = f"{question_code}_{safe_name}_{language_suffix}.png"
        
        print(f"✅ Český název souboru: {png_filename_cs}")
        
        # Test anglického generátoru
        generator_en = EnhancedChartGenerator(language="en-US")
        
        translated_name = "Please provide institution name"
        safe_name_en = translated_name.replace(' ', '_').replace('/', '_').replace('\\', '_')
        if len(safe_name_en) > 100:
            safe_name_en = safe_name_en[:100] + "..."
        
        language_suffix_en = generator_en.language.replace('-', '_')  # en-US → en_US
        png_filename_en = f"{question_code}_{safe_name_en}_{language_suffix_en}.png"
        
        print(f"✅ Anglický název souboru: {png_filename_en}")
        
        # Kontrola, že jsou různé
        if png_filename_cs != png_filename_en:
            print("✅ Názvy souborů se liší podle jazyka")
            print(f"   Český:    {png_filename_cs}")
            print(f"   Anglický: {png_filename_en}")
            return True
        else:
            print("❌ Názvy souborů jsou stejné")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        return False

def test_multiple_languages_no_conflict():
    """Test, že více jazyků nevytváří konflikty souborů"""
    print("\n🧪 Test konfliktů souborů mezi jazyky...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        # Simulace různých jazyků
        languages = ["cs-CZ", "en-US", "de-DE", "fr-FR"]
        question_code = "G2Q00001"
        
        # Názvy v různých jazycích
        names = {
            "cs-CZ": "Jaký je typ Vaší organizace",
            "en-US": "What type of organization",
            "de-DE": "Welche Art von Organisation",
            "fr-FR": "Quel type d'organisation"
        }
        
        filenames = []
        
        for lang in languages:
            generator = EnhancedChartGenerator(language=lang)
            name = names[lang]
            
            safe_name = name.replace(' ', '_').replace('/', '_').replace('\\', '_')[:50]
            language_suffix = generator.language.replace('-', '_')
            filename = f"{question_code}_{safe_name}_{language_suffix}.png"
            
            filenames.append(filename)
            print(f"✅ {lang}: {filename}")
        
        # Kontrola, že všechny názvy jsou unikátní
        if len(set(filenames)) == len(filenames):
            print("✅ Všechny názvy souborů jsou unikátní")
            return True
        else:
            print("❌ Některé názvy souborů se opakují")
            duplicates = [f for f in filenames if filenames.count(f) > 1]
            print(f"   Duplicity: {set(duplicates)}")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        return False

def test_directory_structure():
    """Test struktury adresářů pro různé jazyky"""
    print("\n🧪 Test struktury adresářů...")
    
    # Simulace výsledné struktury
    expected_structure = {
        "charts/827822/": [
            "G1Q00001_Uveďte_prosím_název_instituce_cs_CZ.png",
            "G1Q00001_Please_provide_institution_name_en_US.png",
            "G2Q00001_Jaký_je_typ_Vaší_organizace_cs_CZ.png", 
            "G2Q00001_What_type_of_organization_en_US.png",
            "G3Q00001_Které_oblasti_práva_EU_cs_CZ.png",
            "G3Q00001_Which_areas_of_EU_law_en_US.png"
        ]
    }
    
    print("✅ Očekávaná struktura adresářů:")
    for directory, files in expected_structure.items():
        print(f"   {directory}")
        for file in files:
            print(f"     ├── {file}")
    
    print("\n✅ Výhody nové struktury:")
    print("   • Žádné přepisování souborů")
    print("   • Jasné rozlišení jazyků")
    print("   • Možnost mít všechny verze současně")
    print("   • Snadná identifikace jazyka ze jména souboru")
    
    return True

def test_workflow_example():
    """Test příkladu workflow s více jazyky"""
    print("\n🧪 Test workflow s více jazyky...")
    
    workflow_steps = [
        "1. Menu 6: Generování chart_data.json",
        "2. Menu 10 → Volba 1 → cs-CZ: Vytvoření české šablony",
        "3. Menu 10 → Volba 2: Editace českých překladů",
        "4. Menu 10 → Volba 3 → cs-CZ: Aplikace českých překladů",
        "5. Menu 10 → Volba 5 → cs-CZ: Nastavení českého jazyka",
        "6. Menu 8: Generování českých grafů",
        "   → Soubory: G1Q00001_Název_otázky_cs_CZ.png",
        "",
        "7. Menu 10 → Volba 1 → en-US: Vytvoření anglické šablony", 
        "8. Menu 10 → Volba 2: Editace anglických překladů",
        "9. Menu 10 → Volba 3 → en-US: Aplikace anglických překladů",
        "10. Menu 10 → Volba 5 → en-US: Nastavení anglického jazyka",
        "11. Menu 8: Generování anglických grafů",
        "    → Soubory: G1Q00001_Question_title_en_US.png",
        "",
        "Výsledek: Oba jazyky současně v charts/827822/"
    ]
    
    print("✅ Kompletní workflow pro více jazyků:")
    for step in workflow_steps:
        if step:
            print(f"   {step}")
        else:
            print()
    
    return True

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test názvu PNG souborů s jazykem pro rozlišení verzí")
    print("=" * 70)
    
    tests = [
        test_png_filename_with_language,
        test_multiple_languages_no_conflict,
        test_directory_structure,
        test_workflow_example
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 70)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Názvy PNG souborů s jazykem fungují!")
        print("\n📋 Co bylo implementováno:")
        print("   • Jazyk v názvu PNG souboru (cs_CZ, en_US)")
        print("   • Žádné přepisování mezi jazyky")
        print("   • Možnost mít všechny jazykové verze současně")
        print("\n🎯 Výsledné názvy souborů:")
        print("   Čeština: G1Q00001_Název_otázky_cs_CZ.png")
        print("   Angličtina: G1Q00001_Question_title_en_US.png")
        print("   Němčina: G1Q00001_Frage_titel_de_DE.png")
        print("\n🚀 Workflow:")
        print("   1. Nastav jazyk (Menu 10 → Volba 5)")
        print("   2. Aplikuj překlady (Menu 10 → Volba 3)")
        print("   3. Generuj grafy (Menu 8)")
        print("   4. Opakuj pro další jazyk")
        print("   → Všechny verze se uloží s různými názvy!")
        return True
    else:
        print("❌ Některé části nefungují správně")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
