#!/usr/bin/env python3
"""
Test pro ověření správného pojmenování PNG souborů pro array otázky
"""

import sys
import os
import re

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_png_filename_generation():
    """Test generování názvů PNG souborů"""
    print("🧪 Test generování názvů PNG souborů...")
    
    try:
        # Simulace dat pro array otázku
        question_code = "G2Q00003"
        question_name = "Otázka pole: G2Q00003"  # Generický název z chart_data
        question_text = "J<PERSON><PERSON> jsou vaše zkušenosti s následujícími službami?"  # Skutečný název
        
        # Simulace logiky z enhanced_chart_generator.py
        display_name = question_text if question_text != question_name else question_name
        safe_name = display_name.replace('/', '_').replace('?', '').replace(':', '').replace('"', '').replace('*', '').replace('<', '').replace('>', '').replace('|', '')
        
        # Zkrácení názvu, pokud je příliš dlouhý
        if len(safe_name) > 100:
            safe_name = safe_name[:100] + "..."
        
        png_filename = f"{question_code}_{safe_name}.png"
        
        print(f"✅ Původní název: {question_name}")
        print(f"✅ Skutečný název: {question_text}")
        print(f"✅ Bezpečný název: {safe_name}")
        print(f"✅ PNG soubor: {png_filename}")
        
        # Ověření formátu
        expected_pattern = r"^G2Q00003_.*\.png$"
        if re.match(expected_pattern, png_filename):
            print("✅ Formát názvu souboru je správný")
            
            # Ověření, že neobsahuje generický text
            if "Otázka pole" not in png_filename:
                print("✅ Název neobsahuje generický text 'Otázka pole'")
                return True
            else:
                print("❌ Název stále obsahuje generický text 'Otázka pole'")
                return False
        else:
            print(f"❌ Formát názvu souboru není správný. Očekáváno: {expected_pattern}")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu názvu souboru: {str(e)}")
        return False

def test_safe_filename_characters():
    """Test bezpečných znaků v názvech souborů"""
    print("\n🧪 Test bezpečných znaků v názvech...")
    
    try:
        # Testovací názvy s problematickými znaky
        test_names = [
            "Otázka s / lomítkem",
            "Otázka s ? otazníkem",
            "Otázka s : dvojtečkou",
            'Otázka s " uvozovkami',
            "Otázka s * hvězdičkou",
            "Otázka s < > závorkami",
            "Otázka s | svislítkem"
        ]
        
        for test_name in test_names:
            safe_name = test_name.replace('/', '_').replace('?', '').replace(':', '').replace('"', '').replace('*', '').replace('<', '').replace('>', '').replace('|', '')
            png_filename = f"TEST_{safe_name}.png"
            
            print(f"   '{test_name}' → '{png_filename}'")
            
            # Ověření, že neobsahuje problematické znaky
            problematic_chars = ['/', '?', ':', '"', '*', '<', '>', '|']
            has_problematic = any(char in png_filename for char in problematic_chars)
            
            if has_problematic:
                print(f"❌ Název stále obsahuje problematické znaky: {png_filename}")
                return False
        
        print("✅ Všechny problematické znaky byly odstraněny")
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testu bezpečných znaků: {str(e)}")
        return False

def test_long_filename_truncation():
    """Test zkracování dlouhých názvů"""
    print("\n🧪 Test zkracování dlouhých názvů...")
    
    try:
        # Velmi dlouhý název
        long_name = "Toto je velmi dlouhý název otázky, který by mohl způsobit problémy s délkou názvu souboru v operačním systému a proto ho musíme zkrátit"
        question_code = "G1Q00001"
        
        # Simulace logiky zkracování
        safe_name = long_name.replace('/', '_').replace('?', '').replace(':', '').replace('"', '').replace('*', '').replace('<', '').replace('>', '').replace('|', '')
        
        if len(safe_name) > 100:
            safe_name = safe_name[:100] + "..."
        
        png_filename = f"{question_code}_{safe_name}.png"
        
        print(f"✅ Původní délka: {len(long_name)} znaků")
        print(f"✅ Zkrácený název: {safe_name}")
        print(f"✅ Celková délka názvu souboru: {len(png_filename)} znaků")
        
        # Ověření délky
        if len(png_filename) <= 150:  # Rozumná délka pro název souboru
            print("✅ Délka názvu souboru je v rozumných mezích")
            return True
        else:
            print(f"❌ Název souboru je příliš dlouhý: {len(png_filename)} znaků")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu zkracování: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test pojmenování PNG souborů pro array otázky")
    print("=" * 60)
    
    tests = [
        test_png_filename_generation,
        test_safe_filename_characters,
        test_long_filename_truncation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Všechny testy prošly! Pojmenování PNG souborů je správné.")
        print("\n📋 Shrnutí oprav:")
        print("   • Formát: ID_Skutečný_název_otázky.png")
        print("   • Odstraněny problematické znaky")
        print("   • Zkracování dlouhých názvů")
        print("   • Použití skutečného názvu místo generického")
        return True
    else:
        print("❌ Některé testy selhaly. Zkontrolujte implementaci.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
