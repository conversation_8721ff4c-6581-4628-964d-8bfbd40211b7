#!/usr/bin/env python3
"""
Test opravy question_texts - odstranění neexistujícího pole
"""

import sys
import os
import json
import tempfile

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_extraction_without_question_texts():
    """Test extrakce bez neexistujícího pole question_text"""
    print("🧪 Test extrakce bez question_texts...")
    
    try:
        from translation_manager import TranslationManager
        
        chart_data_path = "src/data/827822/chart_data.json"
        if not os.path.exists(chart_data_path):
            print("❌ Soubor chart_data.json neexistuje")
            return False
        
        tm = TranslationManager("827822")
        strings = tm.extract_translatable_strings(chart_data_path)
        
        print(f"✅ Extrahováno:")
        print(f"   - {len(strings['question_names'])} názvů otá<PERSON>")
        print(f"   - {len(strings['subquestions'])} podotázek")
        print(f"   - {len(strings['response_labels'])} odpovědí")
        
        # Kontrola, že question_texts není v extrakci
        if 'question_texts' not in strings:
            print("✅ question_texts správně odstraněno z extrakce")
        else:
            print("❌ question_texts stále v extrakci")
            return False
        
        # Kontrola škálových odpovědí
        scale_responses = {"rozhodně ano", "spíše ano", "spíše ne", "rozhodně ne", "neumím to posoudit"}
        found_scale = set(strings['response_labels']) & scale_responses
        
        if len(found_scale) >= 4:
            print(f"✅ Škálové odpovědi nalezeny: {found_scale}")
            return True
        else:
            print(f"❌ Škálové odpovědi chybí: {found_scale}")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        return False

def test_template_generation_fixed():
    """Test generování šablony bez question_texts"""
    print("\n🧪 Test generování šablony bez question_texts...")
    
    try:
        from translation_manager import TranslationManager
        
        chart_data_path = "src/data/827822/chart_data.json"
        if not os.path.exists(chart_data_path):
            print("❌ Soubor chart_data.json neexistuje")
            return False
        
        # Vytvoření dočasného adresáře
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_dir = os.path.join(temp_dir, "827822")
            os.makedirs(survey_dir, exist_ok=True)
            
            tm = TranslationManager("827822", "cs-CZ")
            tm.survey_dir = survey_dir
            
            # Generování šablony
            if tm.create_translation_template_for_language(chart_data_path, "cs-CZ"):
                translation_file = os.path.join(survey_dir, "translations_cs-CZ.json")
                
                with open(translation_file, 'r', encoding='utf-8') as f:
                    translations = json.load(f)
                
                print(f"✅ Šablona vygenerována")
                
                # Kontrola struktury
                expected_keys = ['question_names', 'subquestions', 'response_labels']
                for key in expected_keys:
                    if key in translations:
                        count = len(translations[key])
                        print(f"   - {key}: {count} položek")
                    else:
                        print(f"❌ Chybí klíč: {key}")
                        return False
                
                # Kontrola, že question_texts není v šabloně
                if 'question_texts' not in translations:
                    print("✅ question_texts správně odstraněno ze šablony")
                elif len(translations.get('question_texts', {})) == 0:
                    print("✅ question_texts je prázdné (v pořádku)")
                else:
                    print("❌ question_texts stále obsahuje data")
                    return False
                
                # Kontrola škálových odpovědí v šabloně
                response_labels = translations.get('response_labels', {})
                scale_responses = {"rozhodně ano", "spíše ano", "spíše ne", "rozhodně ne", "neumím to posoudit"}
                found_in_template = set(response_labels.keys()) & scale_responses
                
                if len(found_in_template) >= 4:
                    print(f"✅ Škálové odpovědi v šabloně: {found_in_template}")
                    return True
                else:
                    print(f"❌ Škálové odpovědi chybí v šabloně: {found_in_template}")
                    return False
            else:
                print("❌ Nepodařilo se vygenerovat šablonu")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu šablony: {str(e)}")
        return False

def test_statistics_fixed():
    """Test statistik bez question_texts"""
    print("\n🧪 Test statistik bez question_texts...")
    
    try:
        from translation_manager import TranslationManager
        
        chart_data_path = "src/data/827822/chart_data.json"
        if not os.path.exists(chart_data_path):
            print("❌ Soubor chart_data.json neexistuje")
            return False
        
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_dir = os.path.join(temp_dir, "827822")
            os.makedirs(survey_dir, exist_ok=True)
            
            tm = TranslationManager("827822", "cs-CZ")
            tm.survey_dir = survey_dir
            
            # Generování šablony
            if tm.create_translation_template_for_language(chart_data_path, "cs-CZ"):
                # Test statistik
                stats = tm.get_translation_stats()
                
                print(f"✅ Statistiky:")
                for category, data in stats.items():
                    print(f"   - {category}: {data['translated']}/{data['total']} přeloženo")
                
                # Kontrola, že question_texts není ve statistikách
                if 'question_texts' not in stats:
                    print("✅ question_texts správně odstraněno ze statistik")
                else:
                    print("❌ question_texts stále ve statistikách")
                    return False
                
                # Kontrola, že máme rozumné počty
                expected_categories = ['question_names', 'subquestions', 'response_labels']
                for category in expected_categories:
                    if category in stats and stats[category]['total'] > 0:
                        print(f"✅ {category} má data: {stats[category]['total']} položek")
                    else:
                        print(f"❌ {category} nemá data")
                        return False
                
                return True
            else:
                print("❌ Nepodařilo se vygenerovat šablonu")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu statistik: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test opravy question_texts - odstranění neexistujícího pole")
    print("=" * 70)
    
    tests = [
        test_extraction_without_question_texts,
        test_template_generation_fixed,
        test_statistics_fixed
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 70)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Oprava question_texts je úspěšná!")
        print("\n📋 Co bylo opraveno:")
        print("   • Odstraněno neexistující pole question_text z extrakce")
        print("   • Odstraněno question_texts ze statistik")
        print("   • Odstraněno question_texts z aplikace překladů")
        print("   • Škálové odpovědi stále fungují správně")
        print("\n🎯 Nyní by Menu 10 mělo zobrazovat:")
        print("   question_names: X/Y přeloženo")
        print("   subquestions: X/Y přeloženo")
        print("   response_labels: X/Y přeloženo (včetně škálových)")
        print("\n   (bez prázdného question_texts: 0/0)")
        return True
    else:
        print("❌ Oprava nefunguje správně")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
