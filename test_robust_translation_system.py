#!/usr/bin/env python3
"""
Test robustního systému překladů s více jazykovými verzemi
"""

import sys
import os
import json
import tempfile
import shutil

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_multiple_language_files():
    """Test vytváření více jazykových souborů"""
    print("🧪 Test více jazykových souborů...")
    
    try:
        from translation_manager import TranslationManager
        
        # Vytvoření dočasného adres<PERSON>ře
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_dir = os.path.join(temp_dir, "827822")
            os.makedirs(survey_dir, exist_ok=True)
            
            # Vytvoření testovacích chart_data
            chart_data = [
                {
                    "code": "G1Q00001",
                    "name": "Testovací otáz<PERSON>",
                    "type": "single_choice",
                    "data": [{"label": "Ano", "value": 10}, {"label": "Ne", "value": 5}]
                }
            ]
            
            chart_data_path = os.path.join(survey_dir, "chart_data.json")
            with open(chart_data_path, 'w', encoding='utf-8') as f:
                json.dump(chart_data, f, ensure_ascii=False, indent=2)
            
            # Test vytvoření šablon pro různé jazyky
            tm = TranslationManager("827822")
            tm.survey_dir = survey_dir
            
            languages_to_test = ["cs-CZ", "en-US", "de-DE"]
            created_files = []
            
            for lang in languages_to_test:
                print(f"✅ Vytvářím šablonu pro {lang}...")
                if tm.create_translation_template_for_language(chart_data_path, lang):
                    expected_file = os.path.join(survey_dir, f"translations_{lang}.json")
                    if os.path.exists(expected_file):
                        created_files.append((lang, expected_file))
                        print(f"   ✓ Vytvořen: {os.path.basename(expected_file)}")
                    else:
                        print(f"   ✗ Soubor nebyl vytvořen: {expected_file}")
                        return False
                else:
                    print(f"   ✗ Chyba při vytváření šablony pro {lang}")
                    return False
            
            # Test existence všech souborů
            available_files = tm.get_available_translation_files()
            print(f"✅ Dostupné soubory: {list(available_files.keys())}")
            
            if len(available_files) >= len(languages_to_test):
                print("✅ Všechny jazykové soubory byly vytvořeny")
                return True
            else:
                print("❌ Některé jazykové soubory chybí")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_overwrite_protection():
    """Test ochrany před přepsáním existujících souborů"""
    print("\n🧪 Test ochrany před přepsáním...")
    
    try:
        from translation_manager import TranslationManager
        
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_dir = os.path.join(temp_dir, "827822")
            os.makedirs(survey_dir, exist_ok=True)
            
            # Vytvoření testovacích dat
            chart_data = [{"code": "G1Q00001", "name": "Test", "type": "single_choice", "data": []}]
            chart_data_path = os.path.join(survey_dir, "chart_data.json")
            with open(chart_data_path, 'w', encoding='utf-8') as f:
                json.dump(chart_data, f)
            
            tm = TranslationManager("827822")
            tm.survey_dir = survey_dir
            
            # Vytvoření prvního souboru
            lang = "en-US"
            if tm.create_translation_template_for_language(chart_data_path, lang):
                print(f"✅ První soubor pro {lang} vytvořen")
                
                # Pokus o vytvoření bez overwrite (mělo by selhat)
                if not tm.create_translation_template_for_language(chart_data_path, lang, overwrite=False):
                    print("✅ Ochrana před přepsáním funguje")
                    
                    # Pokus o vytvoření s overwrite (mělo by projít)
                    if tm.create_translation_template_for_language(chart_data_path, lang, overwrite=True):
                        print("✅ Přepsání s explicitním povolením funguje")
                        return True
                    else:
                        print("❌ Přepsání s povolením nefunguje")
                        return False
                else:
                    print("❌ Ochrana před přepsáním nefunguje")
                    return False
            else:
                print("❌ Nepodařilo se vytvořit první soubor")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu ochrany: {str(e)}")
        return False

def test_language_specific_application():
    """Test aplikace překladů z konkrétního jazykového souboru"""
    print("\n🧪 Test aplikace překladů z konkrétního jazyka...")
    
    try:
        from translation_manager import TranslationManager
        
        with tempfile.TemporaryDirectory() as temp_dir:
            survey_dir = os.path.join(temp_dir, "827822")
            os.makedirs(survey_dir, exist_ok=True)
            
            # Vytvoření testovacích dat
            chart_data = [
                {
                    "code": "G1Q00001",
                    "name": "Testovací otázka",
                    "type": "single_choice",
                    "data": [{"label": "Ano", "value": 10}]
                }
            ]
            
            chart_data_path = os.path.join(survey_dir, "chart_data.json")
            with open(chart_data_path, 'w', encoding='utf-8') as f:
                json.dump(chart_data, f, ensure_ascii=False, indent=2)
            
            tm = TranslationManager("827822")
            tm.survey_dir = survey_dir
            
            # Vytvoření anglické šablony
            if tm.create_translation_template_for_language(chart_data_path, "en-US"):
                # Úprava překladů
                en_file = os.path.join(survey_dir, "translations_en-US.json")
                with open(en_file, 'r', encoding='utf-8') as f:
                    translations = json.load(f)
                
                # Přidání překladů
                translations['question_names']['Testovací otázka'] = 'Test Question'
                translations['response_labels']['Ano'] = 'Yes'
                
                with open(en_file, 'w', encoding='utf-8') as f:
                    json.dump(translations, f, ensure_ascii=False, indent=2)
                
                # Test aplikace překladů
                output_path = os.path.join(survey_dir, "chart_data_en.json")
                if tm.apply_translations_from_language(chart_data_path, "en-US", output_path):
                    # Ověření výsledku
                    with open(output_path, 'r', encoding='utf-8') as f:
                        translated_data = json.load(f)
                    
                    if (translated_data[0]['name'] == 'Test Question' and 
                        translated_data[0]['data'][0]['label'] == 'Yes'):
                        print("✅ Překlady z konkrétního jazyka byly úspěšně aplikovány")
                        return True
                    else:
                        print("❌ Překlady nebyly správně aplikovány")
                        print(f"Název: {translated_data[0]['name']}")
                        print(f"Label: {translated_data[0]['data'][0]['label']}")
                        return False
                else:
                    print("❌ Nepodařilo se aplikovat překlady")
                    return False
            else:
                print("❌ Nepodařilo se vytvořit anglickou šablonu")
                return False
        
    except Exception as e:
        print(f"❌ Chyba při testu aplikace: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce"""
    print("🚀 Test robustního systému překladů s více jazykovými verzemi")
    print("=" * 70)
    
    tests = [
        test_multiple_language_files,
        test_overwrite_protection,
        test_language_specific_application
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 70)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Robustní systém překladů funguje!")
        print("\n📋 Co bylo implementováno:")
        print("   • Separátní soubory pro každý jazyk (translations_cs-CZ.json, atd.)")
        print("   • Ochrana před přepsáním existujících souborů")
        print("   • Výběr jazyka při generování šablony")
        print("   • Výběr souboru při aplikaci překladů")
        print("   • Menu 10 → Volba 6: Správa jazykových verzí")
        print("\n🎯 Workflow:")
        print("   1. Menu 10 → Volba 1 → Vyberte jazyk → Šablona vytvořena")
        print("   2. Editujte translations_JAZYK.json")
        print("   3. Menu 10 → Volba 3 → Vyberte soubor → Aplikujte překlady")
        print("   4. Opakujte pro další jazyky")
        print("\n🛡️  Bezpečnost:")
        print("   • Žádné přepsání bez potvrzení")
        print("   • Každý jazyk má vlastní soubor")
        print("   • Možnost správy a kontroly souborů")
        return True
    else:
        print("❌ Některé části robustního systému nefungují.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
