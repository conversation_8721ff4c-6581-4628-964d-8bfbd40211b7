#!/usr/bin/env python3
"""
Kompletní test řazení řádků - vyt<PERSON><PERSON><PERSON> testovací graf a ověří řazení
"""

import sys
import os
import json
import tempfile
import pandas as pd

# Přidání src do cesty
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_data():
    """Vytvoří testovací data s jasn<PERSON> r<PERSON>lnými hodnotami"""
    
    # Testovací data s různými hodnotami pro snadné ověření řazení
    test_data = [
        # Kategorie s nejvyš<PERSON> hodnotou "Ano" (30) - měla by být první
        {'id': 1, 'question_code': 'TESTQ001[SQ001]', 'response': 'Ano', 'question_name': '<PERSON><PERSON>ie HIGH (30 Ano)'},
        {'id': 2, 'question_code': 'TESTQ001[SQ001]', 'response': 'Ano', 'question_name': '<PERSON><PERSON><PERSON>IGH (30 Ano)'},
        {'id': 3, 'question_code': 'TESTQ001[SQ001]', 'response': 'Ne', 'question_name': 'Kategorie HIGH (30 Ano)'},
        
        # Kategorie se střední hodnotou "Ano" (20) - měla by být druhá
        {'id': 4, 'question_code': 'TESTQ001[SQ002]', 'response': 'Ano', 'question_name': 'Kategorie MEDIUM (20 Ano)'},
        {'id': 5, 'question_code': 'TESTQ001[SQ002]', 'response': 'Ne', 'question_name': 'Kategorie MEDIUM (20 Ano)'},
        
        # Kategorie s nejnižší hodnotou "Ano" (10) - měla by být třetí
        {'id': 6, 'question_code': 'TESTQ001[SQ003]', 'response': 'Ano', 'question_name': 'Kategorie LOW (10 Ano)'},
        {'id': 7, 'question_code': 'TESTQ001[SQ003]', 'response': 'Ne', 'question_name': 'Kategorie LOW (10 Ano)'},
        {'id': 8, 'question_code': 'TESTQ001[SQ003]', 'response': 'Ne', 'question_name': 'Kategorie LOW (10 Ano)'},
        {'id': 9, 'question_code': 'TESTQ001[SQ003]', 'response': 'Ne', 'question_name': 'Kategorie LOW (10 Ano)'},
    ]
    
    # Přidáme více záznamů pro kategorie HIGH a MEDIUM
    for i in range(10, 40):  # 30 záznamů pro HIGH
        test_data.append({
            'id': i, 
            'question_code': 'TESTQ001[SQ001]', 
            'response': 'Ano', 
            'question_name': 'Kategorie HIGH (30 Ano)'
        })
    
    for i in range(40, 60):  # 20 záznamů pro MEDIUM
        test_data.append({
            'id': i, 
            'question_code': 'TESTQ001[SQ002]', 
            'response': 'Ano', 
            'question_name': 'Kategorie MEDIUM (20 Ano)'
        })
    
    for i in range(60, 70):  # 10 záznamů pro LOW
        test_data.append({
            'id': i, 
            'question_code': 'TESTQ001[SQ003]', 
            'response': 'Ano', 
            'question_name': 'Kategorie LOW (10 Ano)'
        })
    
    return test_data

def test_chart_generation_with_sorting():
    """Test generování grafu s řazením řádků"""
    print("🧪 Test generování grafu s řazením řádků...")
    
    try:
        from enhanced_chart_generator import EnhancedChartGenerator
        
        # Vytvoření testovacích dat
        test_data = create_test_data()
        df = pd.DataFrame(test_data)
        
        print(f"✅ Vytvořeno {len(test_data)} testovacích záznamů")
        
        # Spočítáme hodnoty pro každou kategorii
        category_counts = {}
        for item in test_data:
            question = item['question_code']
            response = item['response']
            name = item['question_name']
            
            if question not in category_counts:
                category_counts[question] = {'name': name, 'Ano': 0, 'Ne': 0}
            
            category_counts[question][response] += 1
        
        print(f"✅ Počty odpovědí podle kategorií:")
        for question, counts in category_counts.items():
            total = counts['Ano'] + counts['Ne']
            print(f"   {counts['name']}: Ano={counts['Ano']}, Ne={counts['Ne']}, Celkem={total}")
        
        # Očekávané pořadí podle hodnot "Ano" (sestupně)
        expected_order = [
            'Kategorie HIGH (30 Ano)',    # 30 Ano - první
            'Kategorie MEDIUM (20 Ano)',  # 20 Ano - druhá  
            'Kategorie LOW (10 Ano)'      # 10 Ano - třetí
        ]
        
        print(f"✅ Očekávané pořadí podle 'Ano' hodnot: {expected_order}")
        
        # Simulace chart_data.json struktury
        chart_data = [{
            'code': 'TESTQ001',
            'name': 'Test Array Question - Řazení řádků',
            'type': 'array',
            'data': []
        }]
        
        for question, counts in category_counts.items():
            chart_data[0]['data'].append({
                'subquestion': counts['name'],
                'responses': {
                    'Ano': counts['Ano'],
                    'Ne': counts['Ne']
                }
            })
        
        print(f"✅ Chart data struktura vytvořena s {len(chart_data[0]['data'])} subotázkami")
        
        # Test vytvoření Datawrapper dat
        generator = EnhancedChartGenerator(
            survey_title="Test Survey - Row Sorting",
            data_source="Test Data",
            language="cs-CZ"
        )
        
        # Simulace vytvoření dat pro Datawrapper
        df_data = []
        for item in chart_data[0]['data']:
            row = {'Kategorie': item['subquestion']}
            for response, count in item['responses'].items():
                row[response] = count
            df_data.append(row)
        
        print(f"✅ Datawrapper data:")
        for row in df_data:
            print(f"   {row}")
        
        # Test metadat pro řazení
        value_columns = ['Ano', 'Ne']
        
        metadata = {
            'visualize': {
                'resort-bars': True,
                'sort-asc': False,
                'sort-by': value_columns[0] if value_columns else None,
            }
        }
        
        print(f"✅ Metadata pro řazení:")
        print(f"   resort-bars: {metadata['visualize']['resort-bars']}")
        print(f"   sort-asc: {metadata['visualize']['sort-asc']}")
        print(f"   sort-by: {metadata['visualize']['sort-by']}")
        
        if metadata['visualize']['sort-by'] == 'Ano':
            print("✅ Řazení podle sloupce 'Ano' je správně nastaveno")
            print("✅ Řádky by měly být seřazené: HIGH (30) → MEDIUM (20) → LOW (10)")
            return True
        else:
            print("❌ Řazení není správně nastaveno")
            return False
        
    except Exception as e:
        print(f"❌ Chyba při testu: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_create_actual_chart():
    """Test vytvoření skutečného grafu pro ověření"""
    print("\n🧪 Test vytvoření skutečného testovacího grafu...")
    
    try:
        # Vytvoření dočasného adresáře pro test
        with tempfile.TemporaryDirectory() as temp_dir:
            test_chart_data_path = os.path.join(temp_dir, "test_chart_data.json")
            
            # Testovací chart_data.json
            test_chart_data = [{
                "code": "TESTQ001",
                "name": "🧪 TEST: Řazení řádků podle hodnot",
                "type": "array",
                "data": [
                    {
                        "subquestion": "🥇 PRVNÍ: Kategorie s 30 Ano odpověďmi",
                        "responses": {
                            "Ano": 30,
                            "Ne": 5
                        }
                    },
                    {
                        "subquestion": "🥉 TŘETÍ: Kategorie s 10 Ano odpověďmi", 
                        "responses": {
                            "Ano": 10,
                            "Ne": 15
                        }
                    },
                    {
                        "subquestion": "🥈 DRUHÁ: Kategorie s 20 Ano odpověďmi",
                        "responses": {
                            "Ano": 20,
                            "Ne": 8
                        }
                    }
                ]
            }]
            
            # Uložení testovacích dat
            with open(test_chart_data_path, 'w', encoding='utf-8') as f:
                json.dump(test_chart_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Testovací chart_data.json vytvořen: {test_chart_data_path}")
            print(f"✅ Testovací data (v nesprávném pořadí):")
            for i, item in enumerate(test_chart_data[0]['data']):
                ano_count = item['responses']['Ano']
                print(f"   {i+1}. {item['subquestion'][:30]}... (Ano: {ano_count})")
            
            print(f"\n✅ Očekávané pořadí po řazení:")
            print(f"   1. 🥇 PRVNÍ: Kategorie s 30 Ano (nejvíc)")
            print(f"   2. 🥈 DRUHÁ: Kategorie s 20 Ano (střed)")
            print(f"   3. 🥉 TŘETÍ: Kategorie s 10 Ano (nejméně)")
            
            print(f"\n🎯 Pro ruční test:")
            print(f"   1. Zkopíruj testovací data do src/data/827822/chart_data.json")
            print(f"   2. Spusť Menu 8")
            print(f"   3. Vygeneruj graf pro TESTQ001")
            print(f"   4. Zkontroluj pořadí řádků v grafu")
            
            return True
        
    except Exception as e:
        print(f"❌ Chyba při vytváření testovacího grafu: {str(e)}")
        return False

def test_manual_verification_guide():
    """Průvodce manuálním ověřením"""
    print("\n🧪 Průvodce manuálním ověřením...")
    
    print("✅ Kroky pro ověření řazení řádků:")
    print("   1. Spusť Menu 8 (Enhanced Chart Generation)")
    print("   2. Najdi graf s array otázkou (např. G6Q00001)")
    print("   3. Zkontroluj pořadí řádků v grafu")
    
    print("\n✅ Co kontrolovat:")
    print("   • Jsou řádky seřazené podle hodnot prvního sloupce?")
    print("   • Je nejvyšší hodnota nahoře?")
    print("   • Je nejnižší hodnota dole?")
    
    print("\n✅ Příklad správného řazení:")
    print("   Pokud první sloupec je 'rozhodně ano':")
    print("   1. Subotázka A: rozhodně ano = 25 ← nahoře")
    print("   2. Subotázka B: rozhodně ano = 15")
    print("   3. Subotázka C: rozhodně ano = 8  ← dole")
    
    print("\n❌ Příklad špatného řazení:")
    print("   1. Subotázka C: rozhodně ano = 8  ← špatně nahoře")
    print("   2. Subotázka A: rozhodně ano = 25")
    print("   3. Subotázka B: rozhodně ano = 15 ← špatně dole")
    
    print("\n🔧 Pokud řazení nefunguje:")
    print("   • Parametr 'sort-by' možná není správný")
    print("   • Zkus 'sort-column': 0 místo 'sort-by'")
    print("   • Možná Datawrapper API používá jiný parametr")
    
    return True

def main():
    """Hlavní testovací funkce"""
    print("🚀 Kompletní test řazení řádků v Datawrapper grafech")
    print("=" * 65)
    
    tests = [
        test_chart_generation_with_sorting,
        test_create_actual_chart,
        test_manual_verification_guide
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 65)
    print(f"📊 Výsledky testů: {passed}/{total} prošlo")
    
    if passed == total:
        print("✅ Kompletní test řazení řádků je připraven!")
        print("\n📋 Test ukázal:")
        print("   • Testovací data s jasně rozlišitelnými hodnotami")
        print("   • Správné nastavení Datawrapper metadat")
        print("   • Očekávané pořadí podle hodnot prvního sloupce")
        print("\n🎯 Nyní spusť Menu 8 a zkontroluj:")
        print("   • Jsou řádky seřazené podle hodnot?")
        print("   • Nejvyšší hodnoty nahoře, nejnižší dole?")
        print("   • Pokud ne, možná je potřeba jiný API parametr")
        return True
    else:
        print("❌ Test řazení řádků má problémy")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
