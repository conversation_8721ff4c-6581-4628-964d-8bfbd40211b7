#!/usr/bin/env python3
"""
Test kompatibility šablon po Menu 5 → Menu 6 → Menu 10
"""

import sys
import os
sys.path.append('src')

import json

def test_template_compatibility():
    """Test kompatibility šablon s chart_data.json"""
    
    survey_id = "827822"
    
    print("🧪 Test kompatibility šablon po Menu 5 → Menu 6 → Menu 10")
    print("=" * 60)
    
    # Změníme do src adresáře
    os.chdir('src')
    
    chart_data_path = f"data/{survey_id}/chart_data.json"
    czech_template_path = f"data/{survey_id}/translations_cs-CZ.json"
    english_template_path = f"data/{survey_id}/translations_en-US.json"
    
    print(f"📁 Kontrola existujících souborů:")
    print(f"   chart_data.json: {os.path.exists(chart_data_path)}")
    print(f"   translations_cs-CZ.json: {os.path.exists(czech_template_path)}")
    print(f"   translations_en-US.json: {os.path.exists(english_template_path)}")
    
    if not os.path.exists(chart_data_path):
        print(f"❌ chart_data.json neexistuje - spusťte Menu 6")
        return
    
    # Načtení chart_data.json
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        chart_data = json.load(f)
    
    chart_names = [item.get('name', '') for item in chart_data]
    print(f"\n📊 Chart_data.json obsahuje {len(chart_names)} grafů:")
    for i, name in enumerate(chart_names[:5]):
        print(f"   [{i+1}]: '{name}'")
    if len(chart_names) > 5:
        print(f"   ... a {len(chart_names) - 5} dalších")
    
    # Test vytvoření českých šablon
    print(f"\n🇨🇿 Test vytvoření české šablony:")
    
    from translation_manager import TranslationManager
    tm = TranslationManager(survey_id)
    
    # Smazání existující české šablony
    if os.path.exists(czech_template_path):
        os.remove(czech_template_path)
        print(f"   🗑️  Smazána existující česká šablona")
    
    # Vytvoření nové české šablony
    success_cz = tm.create_translation_template_for_language(chart_data_path, "cs-CZ", overwrite=True)
    print(f"   Vytvoření: {success_cz}")
    
    if success_cz and os.path.exists(czech_template_path):
        with open(czech_template_path, 'r', encoding='utf-8') as f:
            czech_template = json.load(f)
        
        czech_question_names = czech_template.get('question_names', {})
        print(f"   Počet question_names: {len(czech_question_names)}")
        
        # Kontrola kompatibility klíčů
        czech_keys = set(czech_question_names.keys())
        chart_names_set = set(chart_names)
        
        matching_keys = czech_keys.intersection(chart_names_set)
        missing_in_template = chart_names_set - czech_keys
        extra_in_template = czech_keys - chart_names_set
        
        print(f"   ✅ Shodné klíče: {len(matching_keys)}/{len(chart_names)}")
        if missing_in_template:
            print(f"   ❌ Chybí v šabloně: {len(missing_in_template)}")
            for missing in list(missing_in_template)[:3]:
                print(f"      - '{missing}'")
        if extra_in_template:
            print(f"   ⚠️  Navíc v šabloně: {len(extra_in_template)}")
            for extra in list(extra_in_template)[:3]:
                print(f"      - '{extra}'")
        
        # Kontrola metadat
        metadata = czech_template.get('metadata', {})
        print(f"   Metadata jazyk: {metadata.get('language')}")
        print(f"   Chart jazyk: {czech_template.get('language_settings', {}).get('chart_language')}")
    
    # Test vytvoření anglických šablon
    print(f"\n🇺🇸 Test vytvoření anglické šablony:")
    
    # Smazání existující anglické šablony
    if os.path.exists(english_template_path):
        os.remove(english_template_path)
        print(f"   🗑️  Smazána existující anglická šablona")
    
    # Vytvoření nové anglické šablony
    success_en = tm.create_translation_template_for_language(chart_data_path, "en-US", overwrite=True)
    print(f"   Vytvoření: {success_en}")
    
    if success_en and os.path.exists(english_template_path):
        with open(english_template_path, 'r', encoding='utf-8') as f:
            english_template = json.load(f)
        
        english_question_names = english_template.get('question_names', {})
        print(f"   Počet question_names: {len(english_question_names)}")
        
        # Kontrola kompatibility klíčů
        english_keys = set(english_question_names.keys())
        
        matching_keys_en = english_keys.intersection(chart_names_set)
        missing_in_template_en = chart_names_set - english_keys
        extra_in_template_en = english_keys - chart_names_set
        
        print(f"   ✅ Shodné klíče: {len(matching_keys_en)}/{len(chart_names)}")
        if missing_in_template_en:
            print(f"   ❌ Chybí v šabloně: {len(missing_in_template_en)}")
            for missing in list(missing_in_template_en)[:3]:
                print(f"      - '{missing}'")
        if extra_in_template_en:
            print(f"   ⚠️  Navíc v šabloně: {len(extra_in_template_en)}")
            for extra in list(extra_in_template_en)[:3]:
                print(f"      - '{extra}'")
        
        # Kontrola metadat
        metadata_en = english_template.get('metadata', {})
        print(f"   Metadata jazyk: {metadata_en.get('language')}")
        print(f"   Chart jazyk: {english_template.get('language_settings', {}).get('chart_language')}")
    
    # Porovnání šablon
    if success_cz and success_en:
        print(f"\n🔍 Porovnání šablon:")
        print(f"   České klíče: {len(czech_question_names)}")
        print(f"   Anglické klíče: {len(english_question_names)}")
        print(f"   Shodné klíče mezi šablonami: {len(set(czech_question_names.keys()).intersection(set(english_question_names.keys())))}")
        
        if len(czech_question_names) == len(english_question_names) == len(chart_names):
            print(f"   ✅ Všechny šablony mají správný počet klíčů!")
        else:
            print(f"   ❌ Nesoulad v počtu klíčů!")
    
    print(f"\n🎯 ZÁVĚR:")
    if success_cz and success_en:
        if len(matching_keys) == len(chart_names) and len(matching_keys_en) == len(chart_names):
            print(f"   ✅ Šablony jsou kompatibilní s chart_data.json")
            print(f"   ✅ Můžete bezpečně aplikovat překlady")
        else:
            print(f"   ❌ Šablony NEJSOU plně kompatibilní")
            print(f"   ⚠️  Zkontrolujte chybějící/navíc klíče")
    else:
        print(f"   ❌ Vytvoření šablon selhalo")
    
    # Návrat do původního adresáře
    os.chdir('..')

if __name__ == "__main__":
    test_template_compatibility()
