#!/usr/bin/env python3
"""
Test opravy aplikace překladů - má vytvořit chart_data_{jazyk}.json
"""

import sys
import os
sys.path.append('src')

from translation_manager import TranslationManager
import json
import shutil

def test_translation_application_fix():
    """Test opravy aplikace překladů"""
    
    survey_id = "827822"
    
    print("🧪 Test opravy aplikace překladů")
    print("=" * 60)
    
    # Změníme do src adresáře
    os.chdir('src')
    
    chart_data_path = f"data/{survey_id}/chart_data.json"
    
    # Záloha původního souboru
    backup_path = f"data/{survey_id}/chart_data_backup.json"
    if os.path.exists(chart_data_path):
        shutil.copy2(chart_data_path, backup_path)
        print(f"📁 Vytvořena záloha: {backup_path}")
    
    # Načten<PERSON> původních dat
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    original_first_name = original_data[0].get('name', '') if original_data else ''
    print(f"📊 Původní první název: '{original_first_name}'")
    
    # Test aplikace anglických překladů
    print(f"\n🇺🇸 Test aplikace anglických překladů:")
    
    tm = TranslationManager(survey_id)
    
    # Aplikace překladů (bez output_path - má vytvořit chart_data_en-US.json)
    success = tm.apply_translations_from_language(chart_data_path, "en-US")
    print(f"   Výsledek: {success}")
    
    # Kontrola původního souboru
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        after_data = json.load(f)
    
    after_first_name = after_data[0].get('name', '') if after_data else ''
    print(f"   Původní soubor po aplikaci: '{after_first_name}'")
    print(f"   Původní soubor nezměněn: {original_first_name == after_first_name}")
    
    # Kontrola vytvořeného jazykového souboru
    english_file = f"data/{survey_id}/chart_data_en-US.json"
    print(f"   Anglický soubor: {english_file}")
    print(f"   Existuje: {os.path.exists(english_file)}")
    
    if os.path.exists(english_file):
        with open(english_file, 'r', encoding='utf-8') as f:
            english_data = json.load(f)
        
        english_first_name = english_data[0].get('name', '') if english_data else ''
        print(f"   Anglický první název: '{english_first_name}'")
        print(f"   Je přeložený: {english_first_name != original_first_name}")
    
    # Test aplikace českých překladů
    print(f"\n🇨🇿 Test aplikace českých překladů:")
    
    success_cz = tm.apply_translations_from_language(chart_data_path, "cs-CZ")
    print(f"   Výsledek: {success_cz}")
    
    # Kontrola vytvořeného českého souboru
    czech_file = f"data/{survey_id}/chart_data_cs-CZ.json"
    print(f"   Český soubor: {czech_file}")
    print(f"   Existuje: {os.path.exists(czech_file)}")
    
    if os.path.exists(czech_file):
        with open(czech_file, 'r', encoding='utf-8') as f:
            czech_data = json.load(f)
        
        czech_first_name = czech_data[0].get('name', '') if czech_data else ''
        print(f"   Český první název: '{czech_first_name}'")
        print(f"   Je přeložený: {czech_first_name != original_first_name}")
    
    # Finální kontrola původního souboru
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        final_data = json.load(f)
    
    final_first_name = final_data[0].get('name', '') if final_data else ''
    print(f"\n📊 Finální kontrola původního souboru:")
    print(f"   Původní: '{original_first_name}'")
    print(f"   Finální: '{final_first_name}'")
    print(f"   Nezměněn: {original_first_name == final_first_name}")
    
    if original_first_name == final_first_name:
        print(f"   ✅ ÚSPĚCH: Původní soubor zůstal nezměněn!")
    else:
        print(f"   ❌ PROBLÉM: Původní soubor byl změněn!")
        
        # Obnovení ze zálohy
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, chart_data_path)
            print(f"   🔄 Obnoveno ze zálohy")
    
    # Smazání zálohy
    if os.path.exists(backup_path):
        os.remove(backup_path)
        print(f"   🗑️  Záloha smazána")
    
    print(f"\n🎯 Test dokončen!")
    
    # Návrat do původního adresáře
    os.chdir('..')

if __name__ == "__main__":
    test_translation_application_fix()
