#!/usr/bin/env python3
"""
Debug test pro překlady - porov<PERSON><PERSON><PERSON>
"""

import sys
import os
sys.path.append('src')

import json

def test_translation_debug():
    """Debug test pro překlady"""
    
    survey_id = "827822"
    chart_data_path = f"src/data/{survey_id}/chart_data.json"
    translations_path = f"src/data/{survey_id}/translations_en-US.json"
    
    print("🔍 Debug test pro překlady")
    print("=" * 60)
    
    # Načtení dat
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        chart_data = json.load(f)
    
    with open(translations_path, 'r', encoding='utf-8') as f:
        translations = json.load(f)
    
    print(f"📊 Chart data má {len(chart_data)} grafů")
    print(f"📋 Překlady mají {len(translations.get('question_names', {}))} názvů otázek")
    
    # Porovnání prvních 5 názvů
    print(f"\n🔍 Porovnání n<PERSON>zvů (prvních 5):")
    
    question_names = translations.get('question_names', {})
    
    for i, item in enumerate(chart_data[:5]):
        chart_name = item.get('name', '')
        print(f"\n   Graf {i+1}:")
        print(f"     Chart data: '{chart_name}'")
        
        # Hledání v překladech
        found = False
        if chart_name in question_names:
            print(f"     ✅ Přesná shoda: '{question_names[chart_name]}'")
            found = True
        else:
            # Hledání podobných klíčů
            for key in question_names:
                if key.strip() == chart_name.strip():
                    print(f"     ✅ Shoda po trim: '{question_names[key]}'")
                    found = True
                    break
                elif key in chart_name or chart_name in key:
                    print(f"     🔍 Částečná shoda: '{key}' -> '{question_names[key]}'")
        
        if not found:
            print(f"     ❌ Nenalezeno v překladech")
    
    print(f"\n📋 Všechny klíče v question_names:")
    for i, key in enumerate(list(question_names.keys())[:10]):
        print(f"   [{i+1}]: '{key}'")
    
    print(f"\n📊 Všechny názvy v chart_data:")
    for i, item in enumerate(chart_data[:10]):
        name = item.get('name', '')
        print(f"   [{i+1}]: '{name}'")

if __name__ == "__main__":
    test_translation_debug()
