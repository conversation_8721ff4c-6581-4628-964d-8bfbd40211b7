#!/usr/bin/env python3
"""
Test scénáře: aplikace českých překladů → vytvoření EN šablony
"""

import sys
import os
sys.path.append('src')

from translation_manager import TranslationManager
import json
import shutil

def test_translation_scenario():
    """Test problematického scénáře s překlady"""
    
    survey_id = "827822"
    
    print("🧪 Test scénáře: České překlady → EN šablona")
    print("=" * 60)
    
    # Změníme do src adresáře
    os.chdir('src')
    
    chart_data_path = f"data/{survey_id}/chart_data.json"
    czech_translations_path = f"data/{survey_id}/translations_cs-CZ.json"
    en_translations_path = f"data/{survey_id}/translations_en-US.json"
    
    # Záloha původního chart_data.json
    backup_path = f"data/{survey_id}/chart_data_backup.json"
    shutil.copy2(chart_data_path, backup_path)
    print(f"📁 Vytvořena záloha: {backup_path}")
    
    # Krok 1: Zob<PERSON><PERSON><PERSON> původní<PERSON> názv<PERSON>
    with open(chart_data_path, 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    print(f"\n📊 KROK 1: Původní názvy v chart_data.json")
    original_names = [item.get('name', '') for item in original_data[:5]]
    for i, name in enumerate(original_names):
        print(f"   [{i+1}]: '{name}'")
    
    # Krok 2: Kontrola českých překladů
    print(f"\n🇨🇿 KROK 2: České překlady")
    if os.path.exists(czech_translations_path):
        with open(czech_translations_path, 'r', encoding='utf-8') as f:
            czech_translations = json.load(f)
        
        czech_question_names = czech_translations.get('question_names', {})
        print(f"   Počet českých překladů: {len(czech_question_names)}")
        print(f"   Příklady klíčů: {list(czech_question_names.keys())[:3]}")
        
        # Simulace aplikace českých překladů
        tm = TranslationManager(survey_id)
        print(f"   Aplikuji české překlady...")
        success = tm.apply_translations_from_language(chart_data_path, "cs-CZ", chart_data_path)
        print(f"   Výsledek: {success}")
        
        # Kontrola změn po českých překladech
        with open(chart_data_path, 'r', encoding='utf-8') as f:
            after_czech_data = json.load(f)
        
        after_czech_names = [item.get('name', '') for item in after_czech_data[:5]]
        print(f"   Názvy po českých překladech:")
        for i, name in enumerate(after_czech_names):
            print(f"     [{i+1}]: '{name}'")
        
        # Porovnání
        changed = [orig != new for orig, new in zip(original_names, after_czech_names)]
        print(f"   Změnilo se: {sum(changed)}/{len(changed)} názvů")
        
    else:
        print(f"   ❌ České překlady neexistují: {czech_translations_path}")
    
    # Krok 3: Vytvoření EN šablony po českých překladech
    print(f"\n🇺🇸 KROK 3: Vytvoření EN šablony po českých překladech")
    
    # Smazání existující EN šablony
    if os.path.exists(en_translations_path):
        os.remove(en_translations_path)
        print(f"   🗑️  Smazána existující EN šablona")
    
    # Vytvoření nové EN šablony
    tm = TranslationManager(survey_id)
    success = tm.create_translation_template_for_language(chart_data_path, "en-US", overwrite=True)
    print(f"   Vytvoření EN šablony: {success}")
    
    if success and os.path.exists(en_translations_path):
        with open(en_translations_path, 'r', encoding='utf-8') as f:
            en_template = json.load(f)
        
        en_question_names = en_template.get('question_names', {})
        print(f"   Počet klíčů v EN šabloně: {len(en_question_names)}")
        print(f"   Klíče v EN šabloně:")
        for i, key in enumerate(list(en_question_names.keys())[:5]):
            print(f"     [{i+1}]: '{key}'")
        
        # Porovnání klíčů s původními názvy
        print(f"\n🔍 ANALÝZA: Porovnání klíčů EN šablony s původními názvy")
        en_keys = list(en_question_names.keys())
        matches = 0
        for orig_name in original_names:
            if orig_name in en_keys:
                matches += 1
                print(f"   ✅ Shoda: '{orig_name}'")
            else:
                print(f"   ❌ Neshoda: '{orig_name}' není v EN klíčích")
        
        print(f"\n📊 ZÁVĚR:")
        print(f"   Shoda klíčů s původními názvy: {matches}/{len(original_names)}")
        if matches < len(original_names):
            print(f"   🚨 PROBLÉM POTVRZEN: EN šablona má klíče z přeložených názvů!")
        else:
            print(f"   ✅ EN šablona má správné klíče")
    
    # Obnovení zálohy
    shutil.copy2(backup_path, chart_data_path)
    os.remove(backup_path)
    print(f"\n🔄 Obnovena záloha chart_data.json")
    
    # Návrat do původního adresáře
    os.chdir('..')

if __name__ == "__main__":
    test_translation_scenario()
